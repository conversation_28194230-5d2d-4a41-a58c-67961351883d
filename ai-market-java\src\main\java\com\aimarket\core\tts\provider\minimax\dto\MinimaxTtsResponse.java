package com.aimarket.core.tts.provider.minimax.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Map;

/**
 * Minimax TTS响应参数
 * 
 * <AUTHOR> Market Team
 */
public class MinimaxTtsResponse {

    private Map<String, Object> data;
    
    @JsonProperty("trace_id")
    private String traceId;
    
    @JsonProperty("extra_info")
    private ExtraInfo extraInfo;
    
    @JsonProperty("base_resp")
    private BaseResp baseResp;

    // Constructors
    public MinimaxTtsResponse() {
    }

    // Getters and Setters
    public Map<String, Object> getData() {
        return data;
    }

    public void setData(Map<String, Object> data) {
        this.data = data;
    }

    public String getTraceId() {
        return traceId;
    }

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }

    public ExtraInfo getExtraInfo() {
        return extraInfo;
    }

    public void setExtraInfo(ExtraInfo extraInfo) {
        this.extraInfo = extraInfo;
    }

    public BaseResp getBaseResp() {
        return baseResp;
    }

    public void setBaseResp(BaseResp baseResp) {
        this.baseResp = baseResp;
    }

    /**
     * 额外信息
     */
    public static class ExtraInfo {
        @JsonProperty("audio_length")
        private Long audioLength;
        
        @JsonProperty("audio_sample_rate")
        private Integer audioSampleRate;
        
        @JsonProperty("audio_size")
        private Long audioSize;
        
        private Integer bitrate;
        
        @JsonProperty("audio_format")
        private String audioFormat;
        
        @JsonProperty("audio_channel")
        private Integer audioChannel;
        
        @JsonProperty("invisible_character_ratio")
        private Float invisibleCharacterRatio;
        
        @JsonProperty("usage_characters")
        private Integer usageCharacters;

        // Getters and Setters
        public Long getAudioLength() {
            return audioLength;
        }

        public void setAudioLength(Long audioLength) {
            this.audioLength = audioLength;
        }

        public Integer getAudioSampleRate() {
            return audioSampleRate;
        }

        public void setAudioSampleRate(Integer audioSampleRate) {
            this.audioSampleRate = audioSampleRate;
        }

        public Long getAudioSize() {
            return audioSize;
        }

        public void setAudioSize(Long audioSize) {
            this.audioSize = audioSize;
        }

        public Integer getBitrate() {
            return bitrate;
        }

        public void setBitrate(Integer bitrate) {
            this.bitrate = bitrate;
        }

        public String getAudioFormat() {
            return audioFormat;
        }

        public void setAudioFormat(String audioFormat) {
            this.audioFormat = audioFormat;
        }

        public Integer getAudioChannel() {
            return audioChannel;
        }

        public void setAudioChannel(Integer audioChannel) {
            this.audioChannel = audioChannel;
        }

        public Float getInvisibleCharacterRatio() {
            return invisibleCharacterRatio;
        }

        public void setInvisibleCharacterRatio(Float invisibleCharacterRatio) {
            this.invisibleCharacterRatio = invisibleCharacterRatio;
        }

        public Integer getUsageCharacters() {
            return usageCharacters;
        }

        public void setUsageCharacters(Integer usageCharacters) {
            this.usageCharacters = usageCharacters;
        }
    }

    /**
     * 基础响应
     */
    public static class BaseResp {
        @JsonProperty("status_code")
        private Integer statusCode;
        
        @JsonProperty("status_msg")
        private String statusMsg;

        // Getters and Setters
        public Integer getStatusCode() {
            return statusCode;
        }

        public void setStatusCode(Integer statusCode) {
            this.statusCode = statusCode;
        }

        public String getStatusMsg() {
            return statusMsg;
        }

        public void setStatusMsg(String statusMsg) {
            this.statusMsg = statusMsg;
        }
    }
}
