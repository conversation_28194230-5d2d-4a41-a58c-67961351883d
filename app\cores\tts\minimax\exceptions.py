class MinimaxTTSException(Exception):
    """Minimax TTS 基础异常类"""
    def __init__(self, message: str, status_code: int = None):
        self.message = message
        self.status_code = status_code
        super().__init__(self.message)

class AuthenticationError(MinimaxTTSException):
    """认证错误"""
    pass

class InvalidRequestError(MinimaxTTSException):
    """无效请求错误"""
    pass

class RateLimitError(MinimaxTTSException):
    """限流错误"""
    pass

class APIError(MinimaxTTSException):
    """API 错误"""
    pass

ERROR_MAPPING = {
    1000: ("UnknownError", "未知错误"),
    1001: ("TimeoutError", "请求超时"),
    1002: ("RateLimitError", "触发限流"),
    1004: ("AuthenticationError", "鉴权失败"),
    1039: ("TPMRateLimitError", "触发TPM限流"),
    1042: ("InvalidCharactersError", "非法字符超过10%"),
    2013: ("InvalidFormatError", "输入格式信息不正常")
}
