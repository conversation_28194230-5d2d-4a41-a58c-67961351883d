package com.aimarket.core.tts.provider.minimax.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Map;

/**
 * Minimax TTS请求参数
 * 
 * <AUTHOR> Market Team
 */
public class MinimaxTtsRequest {

    private String model;
    private String text;
    private Boolean stream;
    
    @JsonProperty("voice_setting")
    private VoiceSetting voiceSetting;
    
    @JsonProperty("pronunciation_dict")
    private PronunciationDict pronunciationDict;
    
    @JsonProperty("audio_setting")
    private AudioSetting audioSetting;
    
    @JsonProperty("latex_read")
    private Boolean latexRead;
    
    @JsonProperty("english_normalization")
    private Boolean englishNormalization;
    
    @JsonProperty("language_boost")
    private String languageBoost;
    
    @JsonProperty("subtitle_enable")
    private Boolean subtitleEnable;
    
    @JsonProperty("output_format")
    private String outputFormat;

    // Constructors
    public MinimaxTtsRequest() {
    }

    // Getters and Setters
    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public Boolean getStream() {
        return stream;
    }

    public void setStream(Boolean stream) {
        this.stream = stream;
    }

    public VoiceSetting getVoiceSetting() {
        return voiceSetting;
    }

    public void setVoiceSetting(VoiceSetting voiceSetting) {
        this.voiceSetting = voiceSetting;
    }

    public PronunciationDict getPronunciationDict() {
        return pronunciationDict;
    }

    public void setPronunciationDict(PronunciationDict pronunciationDict) {
        this.pronunciationDict = pronunciationDict;
    }

    public AudioSetting getAudioSetting() {
        return audioSetting;
    }

    public void setAudioSetting(AudioSetting audioSetting) {
        this.audioSetting = audioSetting;
    }

    public Boolean getLatexRead() {
        return latexRead;
    }

    public void setLatexRead(Boolean latexRead) {
        this.latexRead = latexRead;
    }

    public Boolean getEnglishNormalization() {
        return englishNormalization;
    }

    public void setEnglishNormalization(Boolean englishNormalization) {
        this.englishNormalization = englishNormalization;
    }

    public String getLanguageBoost() {
        return languageBoost;
    }

    public void setLanguageBoost(String languageBoost) {
        this.languageBoost = languageBoost;
    }

    public Boolean getSubtitleEnable() {
        return subtitleEnable;
    }

    public void setSubtitleEnable(Boolean subtitleEnable) {
        this.subtitleEnable = subtitleEnable;
    }

    public String getOutputFormat() {
        return outputFormat;
    }

    public void setOutputFormat(String outputFormat) {
        this.outputFormat = outputFormat;
    }

    /**
     * 音色设置
     */
    public static class VoiceSetting {
        @JsonProperty("voice_id")
        private String voiceId;
        
        private Float speed;
        private Float volume;
        private Float pitch;

        // Getters and Setters
        public String getVoiceId() {
            return voiceId;
        }

        public void setVoiceId(String voiceId) {
            this.voiceId = voiceId;
        }

        public Float getSpeed() {
            return speed;
        }

        public void setSpeed(Float speed) {
            this.speed = speed;
        }

        public Float getVolume() {
            return volume;
        }

        public void setVolume(Float volume) {
            this.volume = volume;
        }

        public Float getPitch() {
            return pitch;
        }

        public void setPitch(Float pitch) {
            this.pitch = pitch;
        }
    }

    /**
     * 发音词典
     */
    public static class PronunciationDict {
        private Map<String, String> dict;

        public Map<String, String> getDict() {
            return dict;
        }

        public void setDict(Map<String, String> dict) {
            this.dict = dict;
        }
    }

    /**
     * 音频设置
     */
    public static class AudioSetting {
        @JsonProperty("audio_format")
        private String audioFormat;
        
        @JsonProperty("sample_rate")
        private Integer sampleRate;
        
        private Integer bitrate;
        private Integer channel;

        // Getters and Setters
        public String getAudioFormat() {
            return audioFormat;
        }

        public void setAudioFormat(String audioFormat) {
            this.audioFormat = audioFormat;
        }

        public Integer getSampleRate() {
            return sampleRate;
        }

        public void setSampleRate(Integer sampleRate) {
            this.sampleRate = sampleRate;
        }

        public Integer getBitrate() {
            return bitrate;
        }

        public void setBitrate(Integer bitrate) {
            this.bitrate = bitrate;
        }

        public Integer getChannel() {
            return channel;
        }

        public void setChannel(Integer channel) {
            this.channel = channel;
        }
    }
}
