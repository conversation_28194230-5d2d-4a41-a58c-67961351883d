from abc import ABC, abstractmethod
from typing import Dict, Optional, List
from .schemas import TTSOptions, TTSResult

class BaseTTS(ABC):
    """TTS基类"""
    
    @abstractmethod
    def text_to_speech(self, text: str, voice_id: str, options: Optional[TTSOptions] = None) -> TTSResult:
        """
        文本转语音
        
        Args:
            text: 要转换的文本
            voice_id: 音色ID
            options: 统一的TTS选项参数
            
        Returns:
            TTSResult: 转换结果，包含音频数据和可选的字幕URL
            
        Raises:
            TTSProviderException: 当调用出错时抛出
        """
        pass
    
    @abstractmethod
    def get_voices(self) -> List[Dict]:
        """
        获取可用音色列表
        
        Returns:
            List[Dict]: 音色列表，每个音色包含：
                - voice_id: 音色ID
                - name: 音色名称
                - description: 音色描述
                - type: 音色类型
                
        Raises:
            TTSProviderException: 当调用出错时抛出
        """
        pass
