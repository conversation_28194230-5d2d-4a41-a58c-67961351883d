from flask import request
from flask.typing import ResponseReturnValue
from flask_restful import Resource
from app.controllers.wraps import validate_json
from app.utils.response import APIResponse
from app.schemas.tts import TTSRequestSchema, TTSResponseSchema
from app.services.tts import TTSService
from app.repositories import VoiceRepository


class TestAPI(Resource):
    def get(self):
        voice = VoiceRepository()
        result = voice.get_voice_info()
        raise Exception('测试')

        return APIResponse.success(data=result)
