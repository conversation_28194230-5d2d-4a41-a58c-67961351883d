package com.aimarket.core.tts.provider.minimax;

import com.aimarket.config.ApiKeyProperties;
import com.aimarket.config.AiMarketProperties;
import com.aimarket.core.tts.TtsProvider;
import com.aimarket.core.tts.TtsProviderConfig;
import com.aimarket.core.tts.dto.TtsRequest;
import com.aimarket.core.tts.dto.TtsResponse;
import com.aimarket.core.tts.dto.VoiceInfo;
import com.aimarket.core.tts.exception.TtsProviderException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.hc.client5.http.classic.methods.HttpGet;
import org.apache.hc.client5.http.classic.methods.HttpPost;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.apache.hc.core5.http.ClassicHttpResponse;
import org.apache.hc.core5.http.ContentType;
import org.apache.hc.core5.http.io.entity.EntityUtils;
import org.apache.hc.core5.http.io.entity.StringEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * Minimax TTS提供商实现
 * 
 * <AUTHOR> Market Team
 */
@Component
public class MinimaxTtsProvider implements TtsProvider {

    private static final Logger logger = LoggerFactory.getLogger(MinimaxTtsProvider.class);
    private static final String PROVIDER_NAME = "minimax";

    @Autowired
    private ApiKeyProperties apiKeyProperties;

    @Autowired
    private AiMarketProperties aiMarketProperties;

    private final ObjectMapper objectMapper = new ObjectMapper();
    private final CloseableHttpClient httpClient = HttpClients.createDefault();

    @Override
    public String getProviderName() {
        return PROVIDER_NAME;
    }

    @Override
    public TtsResponse textToSpeech(TtsRequest request) {
        try {
            // 构建Minimax请求
            MinimaxTtsRequest minimaxRequest = buildMinimaxRequest(request);
            
            // 发送HTTP请求
            String responseJson = sendTtsRequest(minimaxRequest);
            
            // 解析响应
            MinimaxTtsResponse minimaxResponse = objectMapper.readValue(responseJson, MinimaxTtsResponse.class);
            
            // 转换为通用响应格式
            return convertToTtsResponse(minimaxResponse);
            
        } catch (Exception e) {
            logger.error("Minimax TTS调用失败", e);
            throw new TtsProviderException("Minimax TTS调用失败: " + e.getMessage(), e, PROVIDER_NAME);
        }
    }

    @Override
    public List<VoiceInfo> getVoices() {
        try {
            // 发送获取音色列表请求
            String responseJson = sendVoicesRequest();
            
            // 解析响应
            MinimaxVoiceResponse voiceResponse = objectMapper.readValue(responseJson, MinimaxVoiceResponse.class);
            
            // 转换为通用音色信息格式
            return convertToVoiceInfoList(voiceResponse);
            
        } catch (Exception e) {
            logger.error("获取Minimax音色列表失败", e);
            throw new TtsProviderException("获取Minimax音色列表失败: " + e.getMessage(), e, PROVIDER_NAME);
        }
    }

    @Override
    public boolean isAvailable() {
        TtsProviderConfig config = getConfig();
        return config.isValid() && !config.getApiKeys().isEmpty();
    }

    @Override
    public TtsProviderConfig getConfig() {
        TtsProviderConfig config = new TtsProviderConfig();
        config.setProviderName(PROVIDER_NAME);
        
        ApiKeyProperties.Minimax minimax = apiKeyProperties.getMinimax();
        if (minimax != null && minimax.getApi() != null) {
            config.setApiKeys(minimax.getApi().getKeys());
            config.setBaseUrl(minimax.getApi().getBaseUrl());
        }
        
        config.setTimeout(aiMarketProperties.getTts().getTimeout());
        config.setRetryCount(aiMarketProperties.getTts().getRetryCount());
        
        return config;
    }

    /**
     * 构建Minimax请求对象
     */
    private MinimaxTtsRequest buildMinimaxRequest(TtsRequest request) {
        MinimaxTtsRequest minimaxRequest = new MinimaxTtsRequest();
        minimaxRequest.setText(request.getText());
        minimaxRequest.setModel(apiKeyProperties.getMinimax().getApi().getDefaultModel());
        minimaxRequest.setStream(request.getStreaming());
        minimaxRequest.setOutputFormat(request.getOutputFormat());
        
        // 设置音色参数
        MinimaxTtsRequest.VoiceSetting voiceSetting = new MinimaxTtsRequest.VoiceSetting();
        voiceSetting.setVoiceId(request.getVoiceId());
        voiceSetting.setSpeed(request.getSpeed());
        voiceSetting.setVolume(request.getVolume());
        minimaxRequest.setVoiceSetting(voiceSetting);
        
        // 设置音频参数
        if (request.getAudioFormat() != null) {
            MinimaxTtsRequest.AudioSetting audioSetting = new MinimaxTtsRequest.AudioSetting();
            audioSetting.setAudioFormat(request.getAudioFormat());
            minimaxRequest.setAudioSetting(audioSetting);
        }
        
        // 设置字幕
        if (request.getSubtitle() != null) {
            minimaxRequest.setSubtitleEnable(request.getSubtitle());
        }
        
        return minimaxRequest;
    }

    /**
     * 发送TTS请求
     */
    private String sendTtsRequest(MinimaxTtsRequest request) throws Exception {
        TtsProviderConfig config = getConfig();
        String apiKey = config.getRandomApiKey();
        
        HttpPost httpPost = new HttpPost(config.getBaseUrl());
        httpPost.setHeader("Authorization", "Bearer " + apiKey);
        httpPost.setHeader("Content-Type", "application/json");
        
        String requestJson = objectMapper.writeValueAsString(request);
        httpPost.setEntity(new StringEntity(requestJson, ContentType.APPLICATION_JSON));
        
        try (ClassicHttpResponse response = httpClient.execute(httpPost)) {
            return EntityUtils.toString(response.getEntity());
        }
    }

    /**
     * 发送获取音色列表请求
     */
    private String sendVoicesRequest() throws Exception {
        TtsProviderConfig config = getConfig();
        String apiKey = config.getRandomApiKey();
        
        String voicesUrl = config.getBaseUrl().replace("/text_to_speech", "/voices");
        HttpGet httpGet = new HttpGet(voicesUrl);
        httpGet.setHeader("Authorization", "Bearer " + apiKey);
        
        try (ClassicHttpResponse response = httpClient.execute(httpGet)) {
            return EntityUtils.toString(response.getEntity());
        }
    }

    /**
     * 转换为通用TTS响应格式
     */
    private TtsResponse convertToTtsResponse(MinimaxTtsResponse minimaxResponse) {
        TtsResponse response = new TtsResponse();
        
        if (minimaxResponse.getData() != null) {
            response.setAudioData((String) minimaxResponse.getData().get("audio"));
        }
        
        response.setTraceId(minimaxResponse.getTraceId());
        
        if (minimaxResponse.getExtraInfo() != null) {
            response.setAudioLength(minimaxResponse.getExtraInfo().getAudioLength());
            response.setAudioSampleRate(minimaxResponse.getExtraInfo().getAudioSampleRate());
            response.setAudioSize(minimaxResponse.getExtraInfo().getAudioSize());
            response.setBitrate(minimaxResponse.getExtraInfo().getBitrate());
            response.setAudioFormat(minimaxResponse.getExtraInfo().getAudioFormat());
            response.setAudioChannel(minimaxResponse.getExtraInfo().getAudioChannel());
        }
        
        return response;
    }

    /**
     * 转换为通用音色信息列表
     */
    private List<VoiceInfo> convertToVoiceInfoList(MinimaxVoiceResponse voiceResponse) {
        List<VoiceInfo> voiceInfoList = new ArrayList<>();
        
        // 处理系统预定义音色
        if (voiceResponse.getSystemVoice() != null) {
            for (MinimaxVoiceResponse.SystemVoice systemVoice : voiceResponse.getSystemVoice()) {
                VoiceInfo voiceInfo = new VoiceInfo();
                voiceInfo.setVoiceId(systemVoice.getVoiceId());
                voiceInfo.setName(systemVoice.getVoiceName());
                voiceInfo.setProvider(PROVIDER_NAME);
                voiceInfo.setDescription(systemVoice.getDescription());
                voiceInfo.setIsPremium(false);
                voiceInfoList.add(voiceInfo);
            }
        }
        
        // 处理快速复刻音色
        if (voiceResponse.getVoiceCloning() != null) {
            for (MinimaxVoiceResponse.VoiceCloning voiceCloning : voiceResponse.getVoiceCloning()) {
                VoiceInfo voiceInfo = new VoiceInfo();
                voiceInfo.setVoiceId(voiceCloning.getVoiceId());
                voiceInfo.setName("复刻音色-" + voiceCloning.getVoiceId());
                voiceInfo.setProvider(PROVIDER_NAME);
                voiceInfo.setDescription(voiceCloning.getDescription());
                voiceInfo.setIsPremium(true);
                voiceInfoList.add(voiceInfo);
            }
        }
        
        return voiceInfoList;
    }
}
