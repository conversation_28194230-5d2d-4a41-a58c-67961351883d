# AI Market Java版本

基于Spring Boot 3的AI服务平台，提供TTS（文本转语音）、图像处理等AI能力的API服务。

## 技术栈

- **Java**: 11
- **Spring Boot**: 3.2.0
- **Maven**: 3.9
- **数据库**: MySQL 8.0 / H2 (测试)
- **缓存**: Redis
- **文档**: SpringDoc OpenAPI 3

## 项目结构

```
ai-market-java/
├── src/main/java/com/aimarket/
│   ├── AiMarketApplication.java          # 主启动类
│   ├── config/                           # 配置类
│   ├── controller/                       # 控制器层
│   │   ├── audio/                        # 音频相关API
│   │   ├── image/                        # 图像相关API
│   │   └── demo/                         # 演示API
│   ├── service/                          # 服务层
│   ├── repository/                       # 数据访问层
│   ├── entity/                          # 实体类
│   ├── dto/                             # 数据传输对象
│   ├── core/                            # 核心业务逻辑
│   │   └── tts/                         # TTS核心模块
│   ├── util/                            # 工具类
│   ├── exception/                       # 异常处理
│   └── middleware/                      # 中间件
├── src/main/resources/
│   ├── application.yml                  # 主配置文件
│   ├── application-dev.yml              # 开发环境配置
│   ├── application-prod.yml             # 生产环境配置
│   ├── application-test.yml             # 测试环境配置
│   └── api-keys.properties              # API密钥配置
└── src/test/java/                       # 测试代码
```

## 核心功能

### 1. TTS服务
- 支持多个TTS提供商（Minimax、Moyin）
- 工厂模式管理不同提供商
- 适配器模式统一API接口
- 负载均衡和故障转移

### 2. 图像处理
- 基础图像处理API
- 支持多种图像格式
- 文件上传和存储管理

### 3. 演示模块
- 用户管理演示
- 产品管理演示
- API使用示例

## 快速开始

### 环境要求
- Java 11+
- Maven 3.9+
- MySQL 8.0+ (生产环境)
- Redis (可选，用于缓存)

### 本地开发

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd ai-market-java
   ```

2. **配置数据库**
   - 创建MySQL数据库：`ai_market_dev`
   - 修改 `application-dev.yml` 中的数据库连接信息

3. **配置API密钥**
   - 复制 `api-keys.properties` 文件
   - 填入真实的API密钥信息

4. **启动应用**
   ```bash
   mvn spring-boot:run
   ```

5. **访问应用**
   - API文档: http://localhost:8080/ai/market/swagger-ui.html
   - 健康检查: http://localhost:8080/ai/market/actuator/health

### 构建部署

```bash
# 构建JAR包
mvn clean package

# 运行JAR包
java -jar target/ai-market-java-1.0.0.jar --spring.profiles.active=prod
```

## API文档

启动应用后，访问 Swagger UI 查看完整的API文档：
http://localhost:8080/ai/market/swagger-ui.html

### 主要API端点

- **TTS API**: `/audio/tts` - 文本转语音
- **音色API**: `/audio/voices` - 获取可用音色列表
- **图像API**: `/v1/image/image` - 图像处理
- **演示API**: `/demo/*` - 演示功能

## 配置说明

### 环境配置
- `dev`: 开发环境，使用本地MySQL和Redis
- `test`: 测试环境，使用H2内存数据库
- `prod`: 生产环境，使用环境变量配置

### 主要配置项
- `aimarket.tts.default-provider`: 默认TTS提供商
- `aimarket.audio.storage-path`: 音频文件存储路径
- `aimarket.security.api-key-enabled`: 是否启用API密钥验证

## 开发指南

### 添加新的TTS提供商

1. 在 `core/tts` 下创建新的提供商包
2. 实现 `TtsProvider` 接口
3. 创建对应的适配器类
4. 在配置文件中添加相关配置

### 添加新的API模块

1. 在 `controller` 下创建新的控制器
2. 在 `service` 下实现业务逻辑
3. 在 `repository` 下添加数据访问层
4. 创建对应的实体类和DTO

## 测试

```bash
# 运行所有测试
mvn test

# 运行特定测试类
mvn test -Dtest=TtsServiceTest

# 生成测试报告
mvn test jacoco:report
```

## 监控

应用集成了Spring Boot Actuator，提供以下监控端点：

- `/actuator/health` - 健康检查
- `/actuator/metrics` - 应用指标
- `/actuator/prometheus` - Prometheus指标

## 许可证

本项目采用 MIT 许可证。详见 [LICENSE](LICENSE) 文件。

## 贡献

欢迎提交Issue和Pull Request来改进项目。

## 联系方式

- 项目维护者: AI Market Team
- 邮箱: <EMAIL>
