"""声音提供语言表模型"""

from sqlalchemy import String, Integer
from sqlalchemy.orm import Mapped, mapped_column

from app.models.base import BaseModel


class GkVoiceLanguage(BaseModel):
    """声音提供语言表"""

    __tablename__ = 'gk_voice_language'

    id: Mapped[int] = mapped_column(
        Integer,
        primary_key=True,
        autoincrement=False,  # 不自动递增，与SQL定义保持一致
        comment='主键ID'
    )
    language: Mapped[str] = mapped_column(
        String(255),
        nullable=True,
        unique=True,  # 唯一约束
        comment='语言code'
    )
    language_name: Mapped[str] = mapped_column(
        String(255),
        nullable=True,
        comment='语言名称'
    )
    sorting: Mapped[int] = mapped_column(
        Integer,
        nullable=True,
        comment='排序'
    )

    def __repr__(self) -> str:
        return f"<GkVoiceLanguage {self.id}: {self.language} - {self.language_name}>"
