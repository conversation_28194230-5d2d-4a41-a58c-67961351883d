import os
from typing import Dict, Any, Optional, List
from app.services.base import BaseService
from app.repositories.voice import VoiceRepository
from app.cores.tts import tts_factory
from app.cores.tts.schemas import TTSOptions
from app.utils.exceptions import ValidationError, NotFoundError
from app.utils.audio import AudioUtils

class TTSService(BaseService):
    """TTS服务"""
    
    def __init__(self):
        """初始化TTS服务"""
        super().__init__(VoiceRepository())
        self.tts_factory = tts_factory

        
    def get_all_voices(self) -> List[Dict[str, Any]]:
        """获取所有音色信息
        
        Returns:
            按语言分组的声音信息列表，包含语言、供应商、音色、价格等信息
        """
        return self.repository.get_voice_info()
        
    def text_to_speech(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """将文本转换为语音
        
        Args:
            params: 转换参数
                text: 要转换的文本
                voice_id: 声音ID
                style: 语气
                speed: 语速
                volume: 音量
                pitch: 音调
                audio_format: 音频格式
                streaming: 是否流式输出
                subtitle: 是否生成字幕
                output_format: 输出格式，默认hex
                
        Returns:
            转换结果
                audio_url: 音频URL
                subtitle_url: 字幕URL(如果请求生成)
                duration: 音频时长
                format: 音频格式
                size: 文件大小
                
        Raises:
            ValidationError: 参数验证失败
            NotFoundError: 声音ID不存在
        """
        # 验证必要参数
        if not params.get('text'):
            raise ValidationError("Text is required")
        if not params.get('voice_id'):
            raise ValidationError("Voice ID is required")

        provider = self.repository.get_provider_by_voice(params['voice_id'])
        speaker = self.repository.get_speaker_by_voice_name_and_style(params['voice_id'], params['style'])

        if provider is None:
            raise NotFoundError(f"Voice ID {params['voice_id']} not found")

        provider_options = {}

        if provider == "minimax":
            provider_options = {
                'model': params.get('model', os.getenv('MINIMAX_DEFAULT_MODEL')),
                'sample_rate': params.get('sample_rate', 32000)
            }
        elif provider == "moyin":
            provider_options = {
                'merge_symbol': params.get('merge_symbol', True),
                'ignore_limit': params.get('ignore_limit', True),
                'rate': params.get('sample_rate', None),
            }

        # 创建TTS选项
        options = TTSOptions(
            style=params.get('style'),
            speed=params.get('speed'),
            volume=params.get('volume'),
            pitch=params.get('pitch'),
            audio_format=params.get('audio_format'),
            streaming=params.get('streaming'),
            subtitle=params.get('subtitle', False),
            provider_options=provider_options
        )


        # 创建TTS实例并执行转换
        tts = self.tts_factory.create(provider)
        result = tts.text_to_speech(
            text=params['text'],
            voice_id=speaker,
            options=options
        )

        if params['output_format'] == "url":
            result.audio_data = AudioUtils.hex_to_mp3_and_upload(result.audio_data)

        # 这里临时mock数据
        return {
            "audio_data": result.audio_data,
            "subtitle_url": result.subtitle_url if options.subtitle else None,
            "format": options.audio_format,
        }
