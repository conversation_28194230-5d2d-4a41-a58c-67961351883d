"""
MSE 网关相关工具类
"""
import os
import json
from typing import Dict
from app.cores.fetcher import api as fetcher


class MSEException(Exception):
    """MSE 网关异常"""
    
    def __init__(self, message: str = "MSE gateway error"):
        self.message = message
        super().__init__(self.message)


class MSEGateway:
    """MSE 网关工具类"""
    
    # MSE 网关 token URL
    TOKEN_URL = os.environ.get("MSE_TOKEN_URL")
    
    @staticmethod
    def get_access_token() -> str:
        """
        获取 MSE token
        
        Returns:
            str: MSE client token
            
        Raises:
            MSEException: 当获取 token 失败时
            json.JSONDecodeError: 当响应内容不是有效的 JSON 格式时
            KeyError: 当响应中缺少必要的字段时
        """
        try:
            response = fetcher.get(MSEGateway.TOKEN_URL)
            token_value = json.loads(response.content)
            client_token = token_value["data"]["client_token"]
            return client_token
        except json.JSONDecodeError as e:
            raise json.JSONDecodeError(f"Failed to parse token response: {str(e)}", e.doc, e.pos)
        except KeyError as e:
            raise KeyError(f"Missing required field in token response: {str(e)}")
        except Exception as e:
            raise MSEException(f"Failed to get MSE token: {str(e)}")
