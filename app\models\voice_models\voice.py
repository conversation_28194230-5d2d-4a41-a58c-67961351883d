"""声音音色表模型"""

from sqlalchemy import String, Integer
from sqlalchemy.orm import Mapped, mapped_column

from app.models.base import BaseModel


class GkVoice(BaseModel):
    """声音音色表"""

    __tablename__ = 'gk_voice'

    id: Mapped[int] = mapped_column(
        Integer,
        primary_key=True,
        autoincrement=False,  # 不自动递增，与SQL定义保持一致
        comment='主键ID'
    )
    voice_name: Mapped[str] = mapped_column(
        String(255),
        nullable=True,
        comment='本地名称'
    )
    display_name: Mapped[str] = mapped_column(
        String(255),
        nullable=True,
        comment='展示名称'
    )

    area: Mapped[str] = mapped_column(
        String(100),
        nullable=True,
        comment='地区'
    )

    gender: Mapped[str] = mapped_column(
        String(255),
        nullable=True,
        comment='性别'
    )
    chinese_gender: Mapped[str] = mapped_column(
        String(255),
        nullable=True,
        comment='中文性别'
    )
    provider: Mapped[str] = mapped_column(
        String(255),
        nullable=True,
        comment='提供商'
    )
    sample_rate_default: Mapped[int] = mapped_column(
        Integer,
        nullable=True,
        comment='默认采样率'
    )
    speaker: Mapped[str] = mapped_column(
        String(255),
        nullable=True,
        comment='音色角色'
    )
    status: Mapped[int] = mapped_column(
        Integer,
        nullable=True,
        comment='状态'
    )

    def __repr__(self) -> str:
        return f"<GkVoice {self.id}: {self.display_name}>"
