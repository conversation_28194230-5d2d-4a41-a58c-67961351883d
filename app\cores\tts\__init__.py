"""
TTS服务统一接口

使用示例:
    from app.cores.tts import tts_factory
    
    # 创建TTS实例（会自动使用随机密钥）
    tts = tts_factory.create('minimax')
    
    # 转换文本为语音
    result = tts.text_to_speech(
        text="要转换的文本",
        voice_id="voice_1",
        options={"speed": 1.0}  # 可选参数
    )
    
    # 获取可用音色列表
    voices = tts.get_voices()
"""

from .factory import TTSFactory
from .exceptions import TTSException, TTSProviderException, TTSConfigException

# 创建工厂类实例
tts_factory = None

def init_tts():
    """初始化TTS服务，确保所有提供商都已注册"""
    # 导入适配器以触发注册
    from .adapters import MinimaxTTSAdapter, MoyinTTSAdapter
    
    # 创建工厂类实例
    global tts_factory
    tts_factory = TTSFactory()
    
    # 返回已注册的提供商列表
    return list(TTSFactory._providers.keys())

# 初始化TTS服务
registered_providers = init_tts()

__all__ = [
    'tts_factory',
    'TTSException',
    'TTSProviderException', 
    'TTSConfigException',
    'init_tts'  # 导出初始化函数，允许在需要时重新初始化
]
