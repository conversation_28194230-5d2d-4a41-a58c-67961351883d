from typing import Any
from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_marshmallow import Marshmallow
from flask_caching import Cache
from flask_cors import CORS

# 创建扩展对象
db = SQLAlchemy()
ma = Marshmallow()
cache = Cache()
cors = CORS()
metrics = None  # 延迟初始化

def config_extensions(app: Flask) -> None:
    """初始化扩展"""
    # SQLAlchemy
    db.init_app(app)

    # Flask-Marshmallow
    ma.init_app(app)

    # Flask-Caching
    cache_config = {
        'CACHE_TYPE': app.config.get('CACHE_TYPE', 'simple'),
        'CACHE_DEFAULT_TIMEOUT': app.config.get('CACHE_DEFAULT_TIMEOUT', 300)
    }
    
    # 如果启用了Redis缓存，添加Redis相关配置
    if app.config.get('ENABLE_REDIS_CACHE', False):
        cache_config.update({
            'CACHE_TYPE': 'redis',
            'CACHE_REDIS_URL': app.config.get('REDIS_URL'),
            'CACHE_KEY_PREFIX': 'flask_cache_',
            'CACHE_OPTIONS': {
                'socket_timeout': 10,
                'retry_on_timeout': True
            }
        })
    app.logger.info(f"Cache config: {cache_config}")
    
    cache.init_app(app, config=cache_config)
    
    # Flask-CORS - 允许所有源访问API
    cors.init_app(app, resources={
        r"/*": {
            "origins": "*",  # 允许所有源
            "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],
            "allow_headers": ["Content-Type", "Authorization", "X-Request-ID"],
            "expose_headers": ["Content-Range", "X-Total-Count"],
            "supports_credentials": True,  # 允许携带认证信息
            "max_age": 600  # 预检请求缓存时间
        }
    })


def ext_blueprints(app: Flask) -> None:
    """注册蓝图"""
    from app.controllers.audio import bp as audio_bp
    from app.controllers.image import bp as image_bp
    from app.controllers.demo import bp as demo_bp

    # 注册蓝图
    app.register_blueprint(audio_bp)
    app.register_blueprint(image_bp)
    app.register_blueprint(demo_bp)
