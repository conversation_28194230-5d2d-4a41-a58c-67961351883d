"""
应用初始化模块
"""

from flask import Flask, jsonify
from flask_cors import CORS
from flask.typing import ResponseReturnValue
from app.config import config as app_config
from app.middlewares.request_id import request_id_middleware
from app.extensions import ext_blueprints
from werkzeug.middleware.dispatcher import DispatcherMiddleware
from werkzeug.exceptions import HTTPException, NotFound
from app.utils.exceptions import APIException
from app.utils.response import APIResponse, ErrorCode
from app.extensions import db, config_extensions
import logging

def config_errorhandler(app: Flask) -> None:
    """配置错误处理器"""
    @app.errorhandler(404)
    def record_not_found(e):
        response = jsonify({'code': 40001, 'message': '记录不存在。', 'data': []}), 404
        return response

    @app.errorhandler(APIException)
    def handle_api_exception(error: APIException) -> ResponseReturnValue:
        """处理自定义API异常"""
        app.logger.error(f"API Exception: {error.message}")
        return APIResponse.error(
            message=error.message,
            code=error.code,
            data=error.data
        )

    @app.errorhandler(HTTPException)
    def handle_http_exception(error: HTTPException) -> ResponseReturnValue:
        """处理HTTP异常"""
        app.logger.error(f"HTTP Exception: {error.description}")
        return APIResponse.error(
            message=str(error.description),
            code=error.code
        )

    @app.errorhandler(Exception)
    def handle_generic_exception(error: Exception) -> ResponseReturnValue:
        """处理通用异常"""
        error_message = str(error)
        app.logger.error(f"Unhandled Exception: {str(error)}", exc_info=True)

        # 根据环境决定是否显示详细错误信息
        if app.config.get('DEBUG', False):
            message = f"Internal server error: {error_message}"
        else:
            message = "Internal server error"

        return APIResponse.error(
            message=message,
            code=ErrorCode.SERVER_ERROR
        )


def create_app(config_name='default'):
    """
    创建Flask应用实例
    
    Args:
        config_name: 配置名称
        
    Returns:
        Flask: Flask应用实例
    """
    app = Flask(__name__)

    # 允许跨域。走网关配置，网关已增加跨域配置。需要把跨域关掉，否则仍然有跨域问题
    CORS(app, resources=r'/*')
    
    # 加载配置
    app.config.from_object(app_config[config_name])
    app_config[config_name].init_app(app)

    # 加载扩展
    config_extensions(app)

    # 注册请求ID中间件
    app.before_request(request_id_middleware)
    
    # 初始化服务
    _init_services()

    
    # 注册蓝图
    ext_blueprints(app)

    # 将整个应用放在 /ai 前缀下
    app.wsgi_app = DispatcherMiddleware(NotFound(), {'/ai/market': app.wsgi_app})

    # 配置错误处理
    config_errorhandler(app)

    # 添加健康检查端点
    @app.route('/health')
    def health_check() -> ResponseReturnValue:
        """健康检查端点"""
        return APIResponse.success(message="Service is healthy")
    
    return app

def _init_services():
    """初始化服务"""
    # 在应用启动时，导入tts模块会自动执行：
    from app.cores.tts import tts_factory, registered_providers
    print(f"已注册的提供商: {registered_providers}")

