import os
from datetime import timedelta
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()
load_dotenv('api_key.env')

BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
STATIC_DIR = os.path.join(BASE_DIR, 'app', 'static')

class Config:
    # 基础配置
    SECRET_KEY = os.getenv('SECRET_KEY', 'dev-key')
    
    # 数据库配置
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_COMMIT_ON_TEARDOWN = True
    SQLALCHEMY_POOL_SIZE = 10
    SQLALCHEMY_POOL_TIMEOUT = 10
    SQLALCHEMY_POOL_RECYCLE = 3600
    
    # JWT配置
    JWT_SECRET_KEY = os.getenv('JWT_SECRET_KEY', 'jwt-secret-key')
    JWT_ACCESS_TOKEN_EXPIRES = timedelta(
        seconds=int(os.getenv('JWT_ACCESS_TOKEN_EXPIRES', 3600))
    )
    
    # Redis配置
    ENABLE_REDIS_CACHE = os.getenv('ENABLE_REDIS_CACHE', 'false').lower() == 'true'
    REDIS_URL = os.getenv('REDIS_URL', 'redis://localhost:6379/0')
    
    # 缓存配置
    CACHE_TYPE = 'redis' if ENABLE_REDIS_CACHE else 'simple'
    CACHE_REDIS_URL = REDIS_URL if ENABLE_REDIS_CACHE else None
    CACHE_DEFAULT_TIMEOUT = 300
    
    # 限流配置
    RATELIMIT_STORAGE_URL = REDIS_URL if ENABLE_REDIS_CACHE else 'memory://'
    RATELIMIT_STRATEGY = 'fixed-window'
    
    @staticmethod
    def init_app(app):
        pass

class DevelopmentConfig(Config):
    DEBUG = True
    SQLALCHEMY_DATABASE_URI = (
        f"mysql+pymysql://{os.getenv('MYSQL_USER', 'user')}:"
        f"{os.getenv('MYSQL_PASSWORD', 'password')}@"
        f"{os.getenv('MYSQL_HOST', 'localhost')}:"
        f"{os.getenv('MYSQL_PORT', '3306')}/"
        f"{os.getenv('MYSQL_DB', 'gchat')}?charset=utf8mb4"
    )
    
    # 开发环境特定配置
    SQLALCHEMY_ECHO = True  # 打印SQL语句
    TEMPLATES_AUTO_RELOAD = True
    
    @classmethod
    def init_app(cls, app):
        Config.init_app(app)
        
        # 开发环境使用控制台日志
        import logging
        logging.basicConfig(level=logging.DEBUG)

class TestingConfig(Config):
    TESTING = True
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'  # 使用内存数据库进行测试
    WTF_CSRF_ENABLED = False
    
    @classmethod
    def init_app(cls, app):
        Config.init_app(app)

class ProductionConfig(Config):
    SQLALCHEMY_DATABASE_URI = (
        f"mysql+pymysql://{os.getenv('MYSQL_USER')}:"
        f"{os.getenv('MYSQL_PASSWORD')}@"
        f"{os.getenv('MYSQL_HOST')}:"
        f"{os.getenv('MYSQL_PORT')}/"
        f"{os.getenv('MYSQL_DB')}?charset=utf8mb4"
    )
    
    SQLALCHEMY_POOL_SIZE = 50
    SQLALCHEMY_MAX_OVERFLOW = 100
    SQLALCHEMY_POOL_RECYCLE = 2 * 3600
    
    @classmethod
    def init_app(cls, app):
        Config.init_app(app)
        
        # 生产环境使用gunicorn的日志处理器
        import logging
        gunicorn_logger = logging.getLogger('gunicorn.error')
        app.logger.handlers = gunicorn_logger.handlers
        app.logger.setLevel(gunicorn_logger.level)
        app.logger.info('Flask App Startup - Using Gunicorn Logger')

config = {
    'development': DevelopmentConfig,
    'testing': TestingConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}
