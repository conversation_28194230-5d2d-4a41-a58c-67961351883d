import gevent.monkey
gevent.monkey.patch_all()

import multiprocessing


# 并行工作进程数, 官方推荐worker数量为cpu核心数量*2+1
workers = 5
threads = 10

backlog = 2048


# 监听内网端口
bind = '0.0.0.0:5000'

# 设置超时时间120s，默认为30s。按自己的需求进行设置
timeout = 120

# 设置访问日志和错误信息日志路径
accesslog = 'logs/gunicorn_access.log'
errorlog = 'logs/gunicorn_error.log'
loglevel = 'debug'
capture_output = True

worker_class = 'gevent'
worker_connections = 6000
max_requests = 10000