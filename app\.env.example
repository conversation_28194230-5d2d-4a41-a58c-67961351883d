# Flask配置
FLASK_ENV=development
FLASK_DEBUG=1
SECRET_KEY=your-secret-key-here

# 数据库配置
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_DB=gchat
MYSQL_USER=user
MYSQL_PASSWORD=password

# Redis配置
ENABLE_REDIS_CACHE=false  # 是否启用Redis缓存
REDIS_URL=redis://localhost:6379/0  # Redis连接地址（仅当ENABLE_REDIS_CACHE=true时生效）

# JWT配置
JWT_SECRET_KEY=your-jwt-secret-key
JWT_ACCESS_TOKEN_EXPIRES=3600

# 日志配置
LOG_LEVEL=INFO
LOG_PATH=logs/app.log
