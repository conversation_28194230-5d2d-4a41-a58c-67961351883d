from flask_restful import Api
from flask import Blueprint

# 创建蓝图
bp = Blueprint('demo', __name__, url_prefix='/demo')

# 创建API实例
api = Api(bp)


from .api_demo import HelloAPI,UserAPI,RequestInfoAPI,ProductAPI,ProductBulkAPI
# 注册API路由
api.add_resource(HelloAPI, '/hello')
api.add_resource(UserAPI, '/user')
api.add_resource(RequestInfoAPI, '/request-info')
api.add_resource(ProductAPI, '/products')
api.add_resource(ProductBulkAPI, '/products/bulk-create')
