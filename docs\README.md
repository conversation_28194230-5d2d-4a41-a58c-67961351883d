# AI Market 项目架构设计文档

## 项目概述

AI Market 是一个基于 Flask 的 AI 服务平台，主要提供文本转语音（TTS）、图像处理等 AI 能力的 API 服务。项目采用分层架构设计，支持多厂商 TTS 服务集成，具备良好的扩展性和可维护性。

## 技术栈

### 后端框架
- **Flask 3.0.0**: Web 应用框架
- **Flask-RESTful**: RESTful API 支持
- **Flask-SQLAlchemy**: ORM 数据库操作
- **Flask-Marshmallow**: 数据序列化/反序列化
- **Flask-Caching**: 缓存支持
- **Flask-CORS**: 跨域资源共享
- **Flask-JWT-Extended**: JWT 认证

### 数据库
- **MySQL**: 主数据库（通过 PyMySQL 连接）
- **Redis**: 缓存数据库（可选）
- **SQLite**: 测试环境数据库

### 部署与运维
- **Docker**: 容器化部署
- **Gunicorn**: WSGI 服务器
- **Prometheus**: 性能监控

### 开发工具
- **pytest**: 单元测试
- **black**: 代码格式化
- **flake8**: 代码检查
- **mypy**: 类型检查

## 项目结构

```
ai-market/
├── app/                          # 应用主目录
│   ├── __init__.py              # Flask 应用工厂
│   ├── config.py                # 配置管理
│   ├── extensions.py            # Flask 扩展初始化
│   ├── controllers/             # 控制器层（API 路由）
│   │   ├── audio/              # 音频相关 API
│   │   ├── image/              # 图像相关 API
│   │   ├── demo/               # 演示 API
│   │   └── wraps.py            # 装饰器
│   ├── cores/                   # 核心业务逻辑
│   │   ├── tts/                # TTS 核心模块
│   │   └── fetcher/            # HTTP 请求工具
│   ├── services/                # 服务层
│   ├── repositories/            # 数据访问层
│   ├── models/                  # 数据模型
│   ├── schemas/                 # 数据验证模式
│   ├── middlewares/             # 中间件
│   └── utils/                   # 工具类
├── docs/                        # 文档目录
├── tests/                       # 测试目录
├── logs/                        # 日志目录
├── requirements.txt             # 依赖包
├── docker-compose.yml           # Docker 编排
├── Dockerfile                   # Docker 镜像
└── manage.py                    # 管理脚本
```

## 架构设计

### 1. 分层架构

项目采用经典的分层架构模式：

```
┌─────────────────┐
│   Controllers   │  ← API 路由层，处理 HTTP 请求
├─────────────────┤
│    Services     │  ← 业务逻辑层，处理业务规则
├─────────────────┤
│  Repositories   │  ← 数据访问层，封装数据操作
├─────────────────┤
│     Models      │  ← 数据模型层，定义数据结构
└─────────────────┘
```

#### Controllers（控制器层）
- 负责处理 HTTP 请求和响应
- 参数验证和数据转换
- 调用服务层处理业务逻辑
- 统一的错误处理和响应格式

#### Services（服务层）
- 实现核心业务逻辑
- 协调多个数据源和外部服务
- 事务管理和业务规则验证
- 提供可复用的业务功能

#### Repositories（数据访问层）
- 封装数据库操作
- 提供统一的数据访问接口
- 支持分页、排序、过滤等通用功能
- 数据库无关的抽象层

#### Models（数据模型层）
- 定义数据库表结构
- 数据验证和约束
- 关系映射和级联操作

### 2. TTS 服务架构

TTS（文本转语音）是项目的核心功能，采用工厂模式和适配器模式设计：

```
┌─────────────────┐
│   TTS Service   │  ← 统一的 TTS 服务接口
├─────────────────┤
│  TTS Factory    │  ← 工厂类，管理多个 TTS 提供商
├─────────────────┤
│   TTS Adapter   │  ← 适配器，统一不同厂商的接口
├─────────────────┤
│ Provider Client │  ← 具体厂商的客户端实现
└─────────────────┘
```

#### 支持的 TTS 提供商
- **Minimax**: 海外 TTS 服务
- **Moyin**: 国内 TTS 服务

#### 核心特性
- **多厂商支持**: 通过适配器模式统一不同厂商的 API
- **负载均衡**: 支持多个 API Key 轮询使用
- **配置管理**: 集中管理各厂商的配置信息
- **错误处理**: 统一的异常处理和错误码
- **扩展性**: 易于添加新的 TTS 提供商

### 3. 配置管理

采用环境变量和配置类的方式管理不同环境的配置：

- **开发环境** (DevelopmentConfig): 启用调试模式，使用本地数据库
- **测试环境** (TestingConfig): 使用内存数据库，禁用 CSRF
- **生产环境** (ProductionConfig): 优化性能配置，启用日志

### 4. 中间件设计

- **请求 ID 中间件**: 为每个请求生成唯一标识，便于日志追踪
- **CORS 中间件**: 处理跨域请求
- **错误处理中间件**: 统一的异常处理和错误响应

### 5. 缓存策略

- **Flask-Caching**: 支持多种缓存后端（内存、Redis）
- **配置化缓存**: 可通过环境变量启用/禁用 Redis 缓存
- **缓存键管理**: 统一的缓存键前缀和过期时间

## 核心功能模块

### 1. 音频处理模块 (app/controllers/audio/)

提供 TTS 相关的 API 服务：

- **TTS API** (`/audio/tts`): 文本转语音接口
- **Voice API** (`/audio/voices`): 获取可用音色列表
- **Test API** (`/audio/test`): 测试接口

### 2. 图像处理模块 (app/controllers/image/)

提供图像处理相关的 API 服务：

- **Image API** (`/v1/image/image`): 图像处理接口
- **Test API** (`/v1/image/test`): 测试接口

### 3. 演示模块 (app/controllers/demo/)

提供演示和测试用的 API：

- **Hello API**: 简单的问候接口
- **User API**: 用户管理演示
- **Product API**: 产品管理演示

## 数据流程

### TTS 服务调用流程

```
1. 客户端请求 → Controllers/audio/tts.py
2. 参数验证 → schemas/tts.py
3. 业务处理 → services/tts.py
4. 厂商选择 → cores/tts/factory.py
5. 适配转换 → cores/tts/adapters/
6. API 调用 → cores/tts/minimax/ 或 cores/tts/moyin/
7. 结果处理 → utils/audio.py
8. 响应返回 → 客户端
```

## 部署架构

### Docker 部署

项目支持 Docker 容器化部署：

- **Dockerfile**: 定义应用镜像构建
- **docker-compose.yml**: 编排应用和依赖服务
- **Gunicorn**: 生产环境 WSGI 服务器

### 服务发现

- **健康检查**: `/health` 端点用于服务健康检查
- **路径前缀**: 所有 API 统一在 `/ai/market` 前缀下

## 扩展性设计

### 1. 新增 TTS 提供商

1. 在 `cores/tts/` 下创建新的提供商目录
2. 实现提供商的客户端类
3. 创建适配器类并注册到工厂
4. 添加配置信息

### 2. 新增 API 模块

1. 在 `controllers/` 下创建新的模块目录
2. 定义 Blueprint 和 API 路由
3. 在 `extensions.py` 中注册 Blueprint
4. 添加对应的 Service、Repository、Model

### 3. 新增中间件

1. 在 `middlewares/` 下创建中间件文件
2. 在应用工厂中注册中间件

## 安全考虑

- **API Key 管理**: 敏感信息通过环境变量管理
- **CORS 配置**: 合理配置跨域访问策略
- **输入验证**: 使用 Marshmallow 进行严格的参数验证
- **错误处理**: 避免敏感信息泄露

## 监控与日志

- **Prometheus 监控**: 集成性能监控指标
- **结构化日志**: 统一的日志格式和级别
- **请求追踪**: 通过请求 ID 进行链路追踪

## 测试策略

- **单元测试**: 使用 pytest 进行单元测试
- **集成测试**: 测试各模块间的集成
- **API 测试**: 测试 RESTful API 接口
- **覆盖率**: 使用 pytest-cov 监控测试覆盖率

## 开发规范

- **代码格式**: 使用 black 进行代码格式化
- **代码检查**: 使用 flake8 进行代码质量检查
- **类型提示**: 使用 mypy 进行类型检查
- **文档**: 完善的代码注释和 API 文档

---

*本文档描述了 AI Market 项目的整体架构设计，为开发和维护提供指导。*
