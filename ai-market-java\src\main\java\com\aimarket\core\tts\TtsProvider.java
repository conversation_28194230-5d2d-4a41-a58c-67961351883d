package com.aimarket.core.tts;

import com.aimarket.core.tts.dto.TtsRequest;
import com.aimarket.core.tts.dto.TtsResponse;
import com.aimarket.core.tts.dto.VoiceInfo;

import java.util.List;

/**
 * TTS提供商接口
 * 定义所有TTS提供商必须实现的方法
 * 
 * <AUTHOR> Market Team
 */
public interface TtsProvider {

    /**
     * 获取提供商名称
     */
    String getProviderName();

    /**
     * 文本转语音
     * 
     * @param request TTS请求参数
     * @return TTS响应结果
     */
    TtsResponse textToSpeech(TtsRequest request);

    /**
     * 获取可用音色列表
     * 
     * @return 音色信息列表
     */
    List<VoiceInfo> getVoices();

    /**
     * 检查提供商是否可用
     * 
     * @return 是否可用
     */
    boolean isAvailable();

    /**
     * 获取提供商配置信息
     * 
     * @return 配置信息
     */
    TtsProviderConfig getConfig();
}
