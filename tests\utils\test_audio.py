import pytest
from pathlib import Path
import binascii
from app.utils.audio import hex_to_mp3


def test_hex_to_mp3_success(tmp_path):
    # 创建一个简单的测试用hex数据（4字节的mp3文件头部）
    test_hex = "494433030000000000"  # ID3v2 tag header
    output_path = tmp_path / "test.mp3"
    
    # 执行转换
    result_path = hex_to_mp3(test_hex, output_path)
    
    # 验证结果
    assert str(result_path).startswith(str(output_path.parent / output_path.stem))
    assert str(result_path).endswith('.mp3')
    assert result_path.exists()
    assert result_path.stat().st_size > 0
    
    # 验证文件内容
    with open(result_path, 'rb') as f:
        content = f.read()
    assert content == binascii.unhexlify(test_hex)


def test_hex_to_mp3_invalid_hex():
    # 测试无效的hex数据
    with pytest.raises(ValueError, match="Invalid hex data provided"):
        hex_to_mp3("invalid hex", Path("test.mp3"))


def test_hex_to_mp3_invalid_path(tmp_path):
    # 测试无效的输出路径（使用一个不存在的深层目录，但这种情况应该能自动创建）
    deep_path = tmp_path / "not" / "exist" / "dir" / "test.mp3"
    test_hex = "494433030000000000"
    
    result_path = hex_to_mp3(test_hex, deep_path)
    
    assert str(result_path).startswith(str(deep_path.parent / deep_path.stem))
    assert str(result_path).endswith('.mp3')
    assert result_path.exists()


def test_hex_to_mp3_empty_hex(tmp_path):
    # 测试空的hex数据
    with pytest.raises(ValueError, match="Invalid hex data provided"):
        hex_to_mp3("", tmp_path / "test.mp3")
