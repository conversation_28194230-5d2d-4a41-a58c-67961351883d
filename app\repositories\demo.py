from typing import Optional
from sqlalchemy import select
from app.models.demo import User
from app.repositories.base import BaseRepository
from app.extensions import db
from app.utils.exceptions import BusinessError

class UserRepository(BaseRepository):
    """用户仓储类"""

    def __init__(self):
        """初始化"""
        super().__init__(User)

    def get_by_username(self, username: str) -> Optional[User]:
        """根据用户名获取用户
        Args:
            username: 用户名
        Returns:
            用户实例或None
        """
        try:
            stmt = select(self.model).filter_by(username=username, is_deleted=False)
            return db.session.scalar(stmt)
        except Exception as e:
            db.session.rollback()
            raise BusinessError(f"Database error: {str(e)}")

    def get_by_email(self, email: str) -> Optional[User]:
        """根据邮箱获取用户
        Args:
            email: 邮箱
        Returns:
            用户实例或None
        """
        try:
            stmt = select(self.model).filter_by(email=email, is_deleted=False)
            return db.session.scalar(stmt)
        except Exception as e:
            db.session.rollback()
            raise BusinessError(f"Database error: {str(e)}")

    def check_username_exists(self, username: str) -> bool:
        """检查用户名是否已存在
        Args:
            username: 用户名
        Returns:
            是否存在
        """
        return self.exists(username=username)

    def check_email_exists(self, email: str) -> bool:
        """检查邮箱是否已存在
        Args:
            email: 邮箱
        Returns:
            是否存在
        """
        return self.exists(email=email)
