# AI Market Java版本主配置文件
server:
  port: 8080
  servlet:
    context-path: /ai/market
  compression:
    enabled: true
    mime-types: application/json,application/xml,text/html,text/xml,text/plain

spring:
  application:
    name: ai-market-java
  
  profiles:
    active: dev
  
  # 数据源配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      idle-timeout: 300000
      connection-timeout: 20000
      max-lifetime: 1200000
      pool-name: AiMarketHikariCP
  
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true
        use_sql_comments: true
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true
    open-in-view: false
  
  # Redis配置
  data:
    redis:
      timeout: 10s
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
          max-wait: -1ms
  
  # 缓存配置
  cache:
    type: redis
    redis:
      time-to-live: 300s
      key-prefix: "ai-market:"
      use-key-prefix: true
      cache-null-values: false
  
  # Jackson配置
  jackson:
    time-zone: Asia/Shanghai
    date-format: yyyy-MM-dd HH:mm:ss
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false
  
  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB

# 日志配置
logging:
  level:
    com.aimarket: INFO
    org.springframework.web: INFO
    org.hibernate.SQL: INFO
    org.hibernate.type.descriptor.sql.BasicBinder: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{requestId}] %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{requestId}] %logger{36} - %msg%n"
  file:
    name: logs/ai-market.log
    max-size: 100MB
    max-history: 30

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
      base-path: /actuator
  endpoint:
    health:
      show-details: when-authorized
  metrics:
    export:
      prometheus:
        enabled: true

# API文档配置
springdoc:
  api-docs:
    path: /api-docs
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
  info:
    title: AI Market API
    description: AI Market Java版本API文档
    version: 1.0.0
    contact:
      name: AI Market Team
      email: <EMAIL>

# 自定义配置
aimarket:
  # TTS配置
  tts:
    # 默认提供商
    default-provider: minimax
    # 请求超时时间（秒）
    timeout: 30
    # 重试次数
    retry-count: 3
    # 缓存配置
    cache:
      voices-ttl: 3600  # 音色列表缓存时间（秒）
      config-ttl: 1800  # 配置缓存时间（秒）
  
  # 音频处理配置
  audio:
    # 支持的音频格式
    supported-formats: mp3,wav,pcm
    # 默认音频格式
    default-format: mp3
    # 音频文件存储路径
    storage-path: /tmp/audio
    # 音频文件URL前缀
    url-prefix: http://localhost:8080/ai/market/audio/files
  
  # 图像处理配置
  image:
    # 支持的图像格式
    supported-formats: jpg,jpeg,png,gif
    # 最大文件大小（MB）
    max-file-size: 10
    # 图像存储路径
    storage-path: /tmp/images
  
  # 安全配置
  security:
    # 是否启用API密钥验证
    api-key-enabled: false
    # 允许的来源
    allowed-origins: "*"
    # 允许的请求头
    allowed-headers: "*"
    # 允许的HTTP方法
    allowed-methods: GET,POST,PUT,DELETE,OPTIONS,PATCH
