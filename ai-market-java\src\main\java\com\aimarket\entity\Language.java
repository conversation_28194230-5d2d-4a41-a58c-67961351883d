package com.aimarket.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

import java.util.HashSet;
import java.util.Set;

/**
 * 语言实体类
 * 对应Python模型中的Language类
 * 
 * <AUTHOR> Market Team
 */
@Entity
@Table(name = "languages", indexes = {
    @Index(name = "idx_language_code", columnList = "code"),
    @Index(name = "idx_language_name", columnList = "name")
})
public class Language extends BaseEntity {

    @NotBlank(message = "语言代码不能为空")
    @Size(max = 10, message = "语言代码长度不能超过10个字符")
    @Column(name = "code", length = 10, nullable = false, unique = true)
    private String code;

    @NotBlank(message = "语言名称不能为空")
    @Size(max = 50, message = "语言名称长度不能超过50个字符")
    @Column(name = "name", length = 50, nullable = false)
    private String name;

    @Size(max = 50, message = "英文名称长度不能超过50个字符")
    @Column(name = "english_name", length = 50)
    private String englishName;

    @Column(name = "is_active", nullable = false)
    private Boolean isActive = true;

    @Column(name = "sort_order", nullable = false)
    private Integer sortOrder = 0;

    @Size(max = 255, message = "描述长度不能超过255个字符")
    @Column(name = "description")
    private String description;

    // 与Voice的关联关系
    @OneToMany(mappedBy = "language", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private Set<Voice> voices = new HashSet<>();

    // Constructors
    public Language() {
    }

    public Language(String code, String name) {
        this.code = code;
        this.name = name;
    }

    // Getters and Setters
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getEnglishName() {
        return englishName;
    }

    public void setEnglishName(String englishName) {
        this.englishName = englishName;
    }

    public Boolean getIsActive() {
        return isActive;
    }

    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Set<Voice> getVoices() {
        return voices;
    }

    public void setVoices(Set<Voice> voices) {
        this.voices = voices;
    }

    // 便利方法
    public void addVoice(Voice voice) {
        voices.add(voice);
        voice.setLanguage(this);
    }

    public void removeVoice(Voice voice) {
        voices.remove(voice);
        voice.setLanguage(null);
    }

    @Override
    public String toString() {
        return "Language{" +
                "id=" + getId() +
                ", code='" + code + '\'' +
                ", name='" + name + '\'' +
                ", englishName='" + englishName + '\'' +
                ", isActive=" + isActive +
                ", sortOrder=" + sortOrder +
                ", createdAt=" + getCreatedAt() +
                ", updatedAt=" + getUpdatedAt() +
                '}';
    }
}
