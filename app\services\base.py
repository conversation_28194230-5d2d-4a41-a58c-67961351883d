from typing import Any, Dict, List, Optional, Type
from app.models.base import BaseModel
from app.repositories.base import BaseRepository
from app.utils.exceptions import ValidationError, NotFoundError

class BaseService:
    """服务基类"""

    def __init__(self, repository: BaseRepository):
        """初始化
        Args:
            repository: 仓储实例
        """
        self.repository = repository

    def get_by_id(self, id_: int) -> Dict[str, Any]:
        """根据ID获取记录
        Args:
            id_: 记录ID
        Returns:
            记录数据字典
        Raises:
            NotFoundError: 记录不存在
        """
        instance = self.repository.get_by_id(id_)
        if not instance:
            raise NotFoundError(f"Record with id {id_} not found")
        return instance.to_dict()

    def get_list(
        self,
        page: int = 1,
        per_page: int = 10,
        only_active: bool = True
    ) -> Dict[str, Any]:
        """获取记录列表
        Args:
            page: 页码
            per_page: 每页数量
            only_active: 是否只返回未删除的记录
        Returns:
            包含分页信息的字典
        """
        # 参数验证
        if page < 1:
            raise ValidationError("Page number must be greater than 0")
        if per_page < 1:
            raise ValidationError("Items per page must be greater than 0")

        return self.repository.get_all(
            page=page,
            per_page=per_page,
            only_active=only_active
        )

    def create(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """创建记录
        Args:
            data: 记录数据
        Returns:
            创建的记录数据字典
        """
        # 数据验证
        self.validate_create(data)
        
        # 创建记录
        instance = self.repository.create(data)
        return instance.to_dict()

    def update(self, id_: int, data: Dict[str, Any]) -> Dict[str, Any]:
        """更新记录
        Args:
            id_: 记录ID
            data: 更新数据
        Returns:
            更新后的记录数据字典
        Raises:
            NotFoundError: 记录不存在
        """
        # 检查记录是否存在
        if not self.repository.exists(id=id_):
            raise NotFoundError(f"Record with id {id_} not found")

        # 数据验证
        self.validate_update(data)
        
        # 更新记录
        instance = self.repository.update(id_, data)
        return instance.to_dict()

    def delete(self, id_: int, hard: bool = False) -> bool:
        """删除记录
        Args:
            id_: 记录ID
            hard: 是否硬删除
        Returns:
            是否删除成功
        Raises:
            NotFoundError: 记录不存在
        """
        # 检查记录是否存在
        if not self.repository.exists(id=id_):
            raise NotFoundError(f"Record with id {id_} not found")
        
        return self.repository.delete(id_, hard)

    def validate_create(self, data: Dict[str, Any]) -> None:
        """创建数据验证
        Args:
            data: 待验证的数据
        Raises:
            ValidationError: 验证失败
        """
        pass

    def validate_update(self, data: Dict[str, Any]) -> None:
        """更新数据验证
        Args:
            data: 待验证的数据
        Raises:
            ValidationError: 验证失败
        """
        pass
