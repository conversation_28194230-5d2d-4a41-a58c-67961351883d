from functools import wraps
from typing import Callable, Optional, Union, Dict, Any, List
from flask import request, current_app
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
from flask_jwt_extended import get_jwt, verify_jwt_in_request

# 创建限流器实例
limiter = Limiter(
    key_func=get_remote_address,
    default_limits=["200 per day", "50 per hour"],
    storage_uri="memory://"
)


def limit_requests(
        limit_value: Union[str, Callable[[], str]],
        key_func: Optional[Callable] = None,
        error_message: Optional[str] = None
) -> Callable:
    """
    API访问频率限制装饰器
    
    Args:
        limit_value: 限制规则，例如："5 per minute", "100/day"
        key_func: 自定义key函数，默认使用IP地址
        error_message: 自定义错误消息
    
    Examples:
        @limit_requests("5 per minute")
        def my_route():
            return "Limited to 5 calls per minute"
            
        @limit_requests("100/day", error_message="Too many requests today!")
        def another_route():
            return "Limited to 100 calls per day"
    """

    def decorator(f: Callable) -> Callable:
        @wraps(f)
        @limiter.limit(
            limit_value,
            key_func=key_func,
            error_message=error_message
        )
        def wrapped(*args, **kwargs):
            return f(*args, **kwargs)

        return wrapped

    return decorator


def admin_required() -> Callable:
    """
    管理员权限要求装饰器
    
    Example:
        @admin_required()
        def admin_only_route():
            return "Only admins can see this"
    """

    def decorator(f: Callable) -> Callable:
        @wraps(f)
        def wrapped(*args, **kwargs):
            verify_jwt_in_request()
            claims = get_jwt()
            if not claims.get("is_admin", False):
                return {"message": "Admin privilege required"}, 403
            return f(*args, **kwargs)

        return wrapped

    return decorator


def cache_control(*directives: str) -> Callable:
    """
    缓存控制装饰器
    
    Args:
        *directives: 缓存指令，例如："no-cache", "private", "max-age=300"
    
    Example:
        @cache_control("private", "max-age=300")
        def cached_route():
            return "This response is cached"
    """

    def decorator(f: Callable) -> Callable:
        @wraps(f)
        def wrapped(*args, **kwargs):
            response = current_app.make_response(f(*args, **kwargs))
            response.headers['Cache-Control'] = ', '.join(directives)
            return response

        return wrapped

    return decorator


def validate_json(*required_fields: str) -> Callable:
    """
    JSON请求体验证装饰器
    
    Args:
        *required_fields: 必需的字段名列表
    
    Example:
        @validate_json("username", "email")
        def create_user():
            # 可以安全地访问request.json中的字段
            return {"status": "success"}
    """

    def decorator(f: Callable) -> Callable:
        @wraps(f)
        def wrapped(*args, **kwargs):
            if not request.is_json:
                return {"message": "Content-Type must be application/json"}, 400

            json_data = request.get_json()
            missing_fields = [field for field in required_fields
                              if field not in json_data]

            if missing_fields:
                return {
                    "message": "Missing required fields",
                    "fields": missing_fields
                }, 400

            return f(*args, **kwargs)

        return wrapped

    return decorator


def log_access(logger=None) -> Callable:
    """
    访问日志记录装饰器
    
    Args:
        logger: 自定义logger对象，如果未提供则使用app.logger
    
    Example:
        @log_access()
        def logged_route():
            return "This access will be logged"
    """

    def decorator(f: Callable) -> Callable:
        @wraps(f)
        def wrapped(*args, **kwargs):
            current_logger = logger or current_app.logger

            # 记录请求信息
            current_logger.info(
                f"Access: {request.method} {request.path} "
                f"from {request.remote_addr}"
            )

            try:
                result = f(*args, **kwargs)
                # 记录响应信息
                current_logger.info(
                    f"Success: {request.method} {request.path} "
                    f"status_code={200 if isinstance(result, tuple) else result[1]}"
                )
                return result
            except Exception as e:
                # 记录错误信息
                current_logger.error(
                    f"Error: {request.method} {request.path} "
                    f"error={str(e)}"
                )
                raise

        return wrapped

    return decorator


def require_params(*params: str) -> Callable:
    """
    URL参数验证装饰器
    
    Args:
        *params: 必需的URL参数名列表
    
    Example:
        @require_params("page", "size")
        def list_items():
            page = request.args.get("page", type=int)
            size = request.args.get("size", type=int)
            return {"page": page, "size": size}
    """

    def decorator(f: Callable) -> Callable:
        @wraps(f)
        def wrapped(*args, **kwargs):
            missing_params = [param for param in params
                              if param not in request.args]

            if missing_params:
                return {
                    "message": "Missing required URL parameters",
                    "params": missing_params
                }, 400

            return f(*args, **kwargs)

        return wrapped

    return decorator
