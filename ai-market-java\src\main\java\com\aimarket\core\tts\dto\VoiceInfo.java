package com.aimarket.core.tts.dto;

import java.math.BigDecimal;
import java.util.List;

/**
 * 音色信息
 * 
 * <AUTHOR> Market Team
 */
public class VoiceInfo {

    private String voiceId;
    private String name;
    private String provider;
    private String language;
    private String languageCode;
    private String gender;
    private String ageGroup;
    private String style;
    private BigDecimal price;
    private String currency;
    private List<String> description;
    private String sampleAudioUrl;
    private Boolean isPremium;
    private List<String> tags;

    // Constructors
    public VoiceInfo() {
    }

    public VoiceInfo(String voiceId, String name, String provider) {
        this.voiceId = voiceId;
        this.name = name;
        this.provider = provider;
    }

    // Getters and Setters
    public String getVoiceId() {
        return voiceId;
    }

    public void setVoiceId(String voiceId) {
        this.voiceId = voiceId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getProvider() {
        return provider;
    }

    public void setProvider(String provider) {
        this.provider = provider;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getLanguageCode() {
        return languageCode;
    }

    public void setLanguageCode(String languageCode) {
        this.languageCode = languageCode;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getAgeGroup() {
        return ageGroup;
    }

    public void setAgeGroup(String ageGroup) {
        this.ageGroup = ageGroup;
    }

    public String getStyle() {
        return style;
    }

    public void setStyle(String style) {
        this.style = style;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public List<String> getDescription() {
        return description;
    }

    public void setDescription(List<String> description) {
        this.description = description;
    }

    public String getSampleAudioUrl() {
        return sampleAudioUrl;
    }

    public void setSampleAudioUrl(String sampleAudioUrl) {
        this.sampleAudioUrl = sampleAudioUrl;
    }

    public Boolean getIsPremium() {
        return isPremium;
    }

    public void setIsPremium(Boolean isPremium) {
        this.isPremium = isPremium;
    }

    public List<String> getTags() {
        return tags;
    }

    public void setTags(List<String> tags) {
        this.tags = tags;
    }

    @Override
    public String toString() {
        return "VoiceInfo{" +
                "voiceId='" + voiceId + '\'' +
                ", name='" + name + '\'' +
                ", provider='" + provider + '\'' +
                ", language='" + language + '\'' +
                ", languageCode='" + languageCode + '\'' +
                ", gender='" + gender + '\'' +
                ", style='" + style + '\'' +
                ", price=" + price +
                ", isPremium=" + isPremium +
                '}';
    }
}
