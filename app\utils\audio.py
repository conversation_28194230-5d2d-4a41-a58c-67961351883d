"""
音频处理工具类
"""
import os
import traceback
import uuid
import time
import json
import binascii
import requests
import io
from typing import Union
from pathlib import Path
from app.cores import fetcher

class AudioException(Exception):
    """音频处理异常"""
    
    def __init__(self, message: str = "Audio processing error"):
        self.message = message
        super().__init__(self.message)


class AudioUtils:
    """音频处理工具类"""
    
    @staticmethod
    def generate_unique_filename(original_path: Union[str, Path, None] = None) -> Union[Path, str]:
        """
        生成唯一的文件名，避免并发写入冲突

        Args:
            original_path (Union[str, Path, None], optional): 原始文件路径，如果为None则只返回唯一标识符

        Returns:
            Union[Path, str]:
                - 如果传入original_path，返回带有唯一标识符的新文件路径(Path对象)
                - 如果不传入参数，返回唯一标识符字符串
        """
        timestamp = int(time.time() * 1000)  # 毫秒级时间戳
        unique_id = str(uuid.uuid4().hex[:8])  # 8位uuid
        unique_suffix = f"{timestamp}_{unique_id}"

        # 如果没有传入原始路径，只返回唯一标识符
        if original_path is None:
            return unique_suffix

        # 如果传入了原始路径，生成完整的唯一文件路径
        path = Path(original_path)
        new_filename = f"{path.stem}_{unique_suffix}{path.suffix}"
        return path.parent / new_filename

    @staticmethod
    def hex_to_mp3(hex_data: str, output_path: Union[str, Path]) -> Path:
        """
        将hex编码的音频数据转换为mp3文件

        Args:
            hex_data (str): hex编码的音频数据
            output_path (Union[str, Path]): 输出mp3文件的路径

        Returns:
            Path: 生成的mp3文件路径

        Raises:
            AudioException: 当处理音频数据失败时
            ValueError: 当hex_data不是有效的hex编码时
            OSError: 当文件写入失败时
        """
        try:
            # 检查hex数据是否为空
            if not hex_data:
                raise ValueError("Invalid hex data provided")
                
            # 生成唯一的文件路径
            unique_path = AudioUtils.generate_unique_filename(output_path)
            
            # 确保输出目录存在
            os.makedirs(unique_path.parent, exist_ok=True)
            
            # 将hex字符串转换为二进制数据
            binary_data = binascii.unhexlify(hex_data)
            
            # 写入mp3文件
            with open(unique_path, 'wb') as f:
                f.write(binary_data)
                
            return unique_path
            
        except binascii.Error:
            raise ValueError("Invalid hex data provided")
        except OSError as e:
            raise OSError(f"Failed to write mp3 file: {str(e)}")
        except Exception as e:
            raise AudioException(f"Failed to process audio data: {str(e)}")

    @staticmethod
    def hex_to_mp3_and_upload(hex_data: str) -> str:
        """
        将hex编码的音频数据转换为mp3并上传到OSS

        Args:
            hex_data (str): hex编码的音频数据
            filename (str): 文件名，默认为audio.mp3

        Returns:
            str: OSS上传后的文件URL

        Raises:
            AudioException: 当处理音频数据失败时
            ValueError: 当hex_data不是有效的hex编码时
            requests.RequestException: 当上传失败时
        """
        try:
            # 检查hex数据是否为空
            if not hex_data:
                raise ValueError("Invalid hex data provided")

            # 将hex字符串转换为二进制数据
            binary_data = binascii.unhexlify(hex_data)

            # 创建内存缓冲区
            mp3_buffer = io.BytesIO(binary_data)

            # 生成唯一文件名（避免重名）
            unique_filename = AudioUtils.generate_unique_filename()
            unique_filename += ".mp3"
            # 组装上传数据
            file_stream = (unique_filename, mp3_buffer, "audio/mp3")
            params = {"dir": "tmp"}

            # 上传到OSS
            print(os.getenv('OSS_URL'))
            upload_result = requests.post(
                url=os.getenv('OSS_URL'),
                files={"file": file_stream},
                params=params
            )

            print(upload_result.text)
            # 检查上传结果
            if upload_result.status_code != 200:
                raise Exception(f"Upload failed with status code: {upload_result.status_code}")

            upload_result_d = json.loads(upload_result.text)

            # 检查返回结果是否包含URL
            if "data" not in upload_result_d or "url" not in upload_result_d["data"]:
                raise AudioException("Upload response does not contain valid URL")

            url = upload_result_d["data"]["url"]
            return url

        except binascii.Error as e:
            raise ValueError(f"Invalid hex data: {str(e)}")
        except json.JSONDecodeError as e:
            raise AudioException(f"Failed to parse upload response: {str(e)}")
        except Exception as e:
            print(traceback.format_exc())
            raise AudioException(f"Unexpected error during hex to mp3 conversion and upload: {str(e)}")