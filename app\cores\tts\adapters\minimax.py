import traceback
import os
from typing import Dict, Optional, List
from ..base import BaseTTS
from ..factory import TTSFactory
from ..exceptions import TTSProviderException
from ..schemas import TTSOptions, TTSResult
from ...tts.minimax.exceptions import MinimaxTTSException

@TTSFactory.register("minimax")
class MinimaxTTSAdapter(BaseTTS):
    """Minimax TTS适配器"""
    
    def __init__(self, api_key: str, group_id: str):
        """
        初始化Minimax TTS适配器
        
        Args:
            api_key: API密钥
            group_id: 用户组ID
        """
        try:
            from ...tts.minimax.tts import MinimaxTTS
            self.tts = MinimaxTTS(api_key, group_id)
        except Exception as e:
            raise TTSProviderException(str(e), "minimax")
    
    def text_to_speech(self, text: str, voice_id: str, options: Optional[TTSOptions] = None) -> TTSResult:
        """
        将文本转换为语音
        
        Args:
            text: 要转换的文本
            voice_id: 音色ID
            options: 统一的TTS选项参数
            
        Returns:
            TTSResult: 转换结果，包含音频数据和可选的字幕URL
            
        Raises:
            TTSProviderException: 当调用出错时抛出
        """
        try:
            from ...tts.minimax.schemas import TTSRequest, VoiceSetting, AudioSetting

            options = options or TTSOptions()

            # 创建音色设置
            voice_setting = VoiceSetting(
                voice_id=voice_id,
                speed=options.speed,
                vol=options.volume,  # 统一使用volume参数
                pitch=int(options.pitch),
                emotion=options.style if options.style else 'neutral',
            )
            # 创建音频设置
            audio_setting = AudioSetting(
                sample_rate=options.provider_options.get('sample_rate'),
                bitrate=options.bitrate,
                format=options.audio_format,
                channel=options.channel
            )

            # 创建请求
            request = TTSRequest(
                text=text,
                voice_setting=voice_setting,
                model=options.provider_options.get('model', os.getenv('MINIMAX_DEFAULT_MODEL')),
                stream=options.streaming,
                subtitle_enable=False,
                audio_setting=audio_setting
            )

            print('minimax_request: {}'.format(request))

            # 添加厂商特定参数
            for key in ['latex_read', 'english_normalization', 'language_boost', 'output_format']:
                if key in options.provider_options:
                    setattr(request, key, options.provider_options[key])

            response = self.tts.text_to_speech(request)

            # 处理响应
            if not response.data:
                raise TTSProviderException("No data in response", "minimax")

            audio_data = response.data.get('audio')
            if not audio_data:
                raise TTSProviderException("No audio data in response", "minimax")

            # 构建结果
            extra_info = {}
            if response.extra_info:
                extra_info = {
                    'audio_length': response.extra_info.audio_length,
                    'audio_sample_rate': response.extra_info.audio_sample_rate,
                    'audio_size': response.extra_info.audio_size,
                    'bitrate': response.extra_info.bitrate,
                    'audio_format': response.extra_info.audio_format,
                    'audio_channel': response.extra_info.audio_channel,
                    'usage_characters': response.extra_info.usage_characters
                }

            return TTSResult(
                audio_data=audio_data,
                subtitle_url=response.data.get('subtitle_url', None),
                extra_info=extra_info
            )
        except MinimaxTTSException as e:
            raise TTSProviderException(str(e), "minimax", e.status_code)
        except Exception as e:
            print(traceback.format_exc())
            raise TTSProviderException(str(e), "minimax")
    
    def get_voices(self) -> List[Dict]:
        """
        获取可用音色列表
        
        Returns:
            List[Dict]: 音色列表，每个音色包含：
                - voice_id: 音色ID
                - name: 音色名称
                - description: 音色描述
                - type: 音色类型 (system/cloning/generation/music)
            
        Raises:
            TTSProviderException: 当调用出错时抛出
        """
        try:
            response = self.tts.get_voice("all")
            voices = []
            
            # 处理系统预定义音色
            if response.system_voice:
                for voice in response.system_voice:
                    voices.append({
                        "voice_id": voice.voice_id,
                        "name": voice.voice_name,
                        "description": voice.description,
                        "type": "minimax_system"
                    })
            
            # 处理快速复刻音色
            if response.voice_cloning:
                for voice in response.voice_cloning:
                    voices.append({
                        "voice_id": voice.voice_id,
                        "name": f"克隆音色 ({voice.created_time})",
                        "description": voice.description,
                        "type": "cloning"
                    })
            
            # 处理生成的音色
            if response.voice_generation:
                for voice in response.voice_generation:
                    voices.append({
                        "voice_id": voice.voice_id,
                        "name": f"生成音色 ({voice.created_time})",
                        "description": voice.description,
                        "type": "generation"
                    })
            
            # 处理音乐生成的音色
            if response.music_generation:
                for voice in response.music_generation:
                    voices.append({
                        "voice_id": voice.voice_id,
                        "name": f"音乐音色 ({voice.created_time})",
                        "description": ["音乐生成音色"],
                        "type": "music"
                    })
                    # 添加伴奏音色
                    voices.append({
                        "voice_id": voice.instrumental_id,
                        "name": f"伴奏音色 ({voice.created_time})",
                        "description": ["音乐生成伴奏"],
                        "type": "music"
                    })
            
            return voices
            
        except MinimaxTTSException as e:
            raise TTSProviderException(str(e), "minimax", e.status_code)
        except Exception as e:
            raise TTSProviderException(str(e), "minimax")
