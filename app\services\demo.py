from typing import Dict, Any
from werkzeug.security import generate_password_hash, check_password_hash
from app.repositories.demo import UserRepository
from app.repositories.base import BaseRepository
from app.services.base import BaseService
from app.utils.exceptions import ValidationError, NotFoundError

image_service = BaseService(BaseRepository)
class UserService(BaseService):
    """用户服务类"""

    def __init__(self):
        """初始化"""
        super().__init__(UserRepository())

    def create_user(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """创建用户
        Args:
            data: 用户数据
        Returns:
            用户信息
        Raises:
            ValidationError: 验证失败
        """
        # 检查用户名是否已存在
        if self.repository.check_username_exists(data['username']):
            raise ValidationError("用户名已存在")

        # 检查邮箱是否已存在
        if self.repository.check_email_exists(data['email']):
            raise ValidationError("邮箱已存在")

        # 密码加密
        data['password'] = generate_password_hash(data['password'])
        
        # 创建用户
        return self.create(data)

    def update_user(self, id_: int, data: Dict[str, Any]) -> Dict[str, Any]:
        """更新用户信息
        Args:
            id_: 用户ID
            data: 更新数据
        Returns:
            更新后的用户信息
        Raises:
            NotFoundError: 用户不存在
            ValidationError: 验证失败
        """
        # 如果包含密码，进行加密
        if 'password' in data:
            data['password'] = generate_password_hash(data['password'])
        
        return self.update(id_, data)

    def verify_password(self, username: str, password: str) -> Dict[str, Any]:
        """验证用户密码
        Args:
            username: 用户名
            password: 密码
        Returns:
            用户信息
        Raises:
            ValidationError: 验证失败
        """
        # 获取用户
        user = self.repository.get_by_username(username)
        if not user:
            raise ValidationError("用户名或密码错误")

        # 验证密码
        if not check_password_hash(user.password, password):
            raise ValidationError("用户名或密码错误")

        return user.to_dict()

    def get_by_username(self, username: str) -> Dict[str, Any]:
        """根据用户名获取用户
        Args:
            username: 用户名
        Returns:
            用户信息
        Raises:
            NotFoundError: 用户不存在
        """
        user = self.repository.get_by_username(username)
        if not user:
            raise NotFoundError(f"User {username} not found")
        return user.to_dict()

    def get_by_email(self, email: str) -> Dict[str, Any]:
        """根据邮箱获取用户
        Args:
            email: 邮箱
        Returns:
            用户信息
        Raises:
            NotFoundError: 用户不存在
        """
        user = self.repository.get_by_email(email)
        if not user:
            raise NotFoundError(f"User with email {email} not found")
        return user.to_dict()
