from typing import Dict, Any, List, Optional
from sqlalchemy import select, func
from sqlalchemy.orm import aliased
from collections import defaultdict

from app.extensions import db
from app.models.voice import GkVoice, GkVoiceLanguage, GkVoiceLanguageRelations, GkVoiceModel, GkVoiceStyleRelations
from app.repositories.base import BaseRepository
from app.utils.cache_utils import cached_by_args


class VoiceRepository(BaseRepository):
    """声音仓储类"""

    def __init__(self):
        """初始化"""
        super().__init__(GkVoice)

    @cached_by_args(timeout=3600)  # 缓存1小时
    def get_voice_info(self) -> List[Dict[str, Any]]:
        """获取声音信息
        
        查询当前支持的所有语种、供应商、音色、价格信息
        
        Returns:
            按语言分组的声音信息列表
        """
        try:
            # 创建别名以匹配SQL查询中的表别名
            a = aliased(GkVoice)
            b = aliased(GkVoiceLanguageRelations)
            c = aliased(GkVoiceStyleRelations)
            d = aliased(GkVoiceModel)
            e = aliased(GkVoiceLanguage)

            # 构建查询
            stmt = (
                select(
                    a.id,
                    a.voice_name,
                    a.display_name,
                    a.area,
                    a.gender,
                    a.chinese_gender,
                    a.provider,
                    a.sample_rate_default,
                    a.speaker.label('voice_speaker'),
                    a.status,
                    b.language,
                    e.language_name,
                    c.id.label('style_id'),
                    c.style,
                    c.style_name,
                    c.speaker.label('style_speaker'),
                    d.model,
                    d.price
                )
                .select_from(a)
                .outerjoin(b, a.id == b.voice_id)
                .outerjoin(c, a.id == c.voice_id)
                .outerjoin(d, a.id == d.voice_id)
                .outerjoin(e, b.language == e.language)
                .where(a.status == 1)
                .where(b.status == 1)
            )

            stmt = stmt.order_by(
                e.sorting
            )#.filter(e.language.in_(['zh', 'es']))

            # 执行查询
            result = db.session.execute(stmt).all()

            # 使用嵌套的defaultdict来构建结果
            language_dict = defaultdict(lambda: defaultdict(list))
            
            # 用于跟踪已处理的voice_id和style_id，避免重复
            processed_voices = {}
            processed_styles = {}
            
            # 用于存储每种语言的名称
            language_names = {}

            # 处理查询结果
            for row in result:
                # 如果没有语言信息，跳过
                if not row.language or not row.language_name:
                    continue

                # 获取语言信息
                language_key = row.language
                language_names[language_key] = row.language_name  # 存储语言名称
                provider = row.provider

                # 处理音色信息
                voice_id = row.id
                voice_key = f"{voice_id}_{provider}_{language_key}"

                # 如果这个音色还没有处理过
                if voice_key not in processed_voices:
                    voice_info = {
                        # 'voice_id': voice_id,
                        'voice_name': row.voice_name,
                        'display_name': row.display_name,
                        'area': row.area,
                        'gender': row.gender,
                        'chinese_gender': row.chinese_gender,
                        'sample_rate_default': row.sample_rate_default,
                        # 'speaker': row.voice_speaker or row.style_speaker,
                        # 'status': row.status,
                        # 'model': row.model,
                        'price': float(row.price) if row.price else None,
                        'StyleList': []
                    }
                    language_dict[language_key][provider].append(voice_info)
                    processed_voices[voice_key] = voice_info
                else:
                    voice_info = processed_voices[voice_key]

                # 处理语气信息
                if row.style_id:
                    # 修改style_key，加入language_key以区分不同语言下的相同style
                    style_key = f"{row.style_id}_{voice_id}_{language_key}"
                    if style_key not in processed_styles:
                        style_info = {
                            'style': row.style,
                            'style_name': row.style_name,
                            # 'speaker': row.style_speaker or row.voice_speaker
                        }
                        voice_info['StyleList'].append(style_info)
                        processed_styles[style_key] = True

            # 转换为最终的列表格式
            result_list = []
            for language, providers in language_dict.items():
                language_item = {
                    'language': language,
                    'language_name': language_names.get(language, '')  # 使用对应的语言名称
                }

                # 添加提供商信息
                for provider, voices in providers.items():
                    language_item[provider] = voices

                result_list.append(language_item)

            return result_list
        except Exception as e:
            db.session.rollback()
            raise e

    @cached_by_args(timeout=3600)
    def get_speaker_by_voice_name_and_style(self, voice_name: str, style: Optional[str] = None) -> Optional[str]:
        """根据voice_name和语气查询speaker
        
        优先使用gk_voice_style_relations表中的speaker，如果没有，则使用gk_voice表中的speaker
        
        Args:
            voice_name: 声音名称
            style: 语气风格，可选
            
        Returns:
            speaker名称，如果未找到则返回None
        """
        try:
            # 创建别名
            v = aliased(GkVoice)
            s = aliased(GkVoiceStyleRelations)

            # 如果提供了style，则查询带有style的speaker
            if style:
                stmt = (
                    select(
                        func.ifnull(s.speaker, v.speaker).label('speaker')
                    )
                    .select_from(v)
                    .outerjoin(s, (v.id == s.voice_id) & (s.style == style))
                    .filter(v.voice_name == voice_name)
                )
            else:
                # 如果没有提供style，则只查询voice表中的speaker
                stmt = (
                    select(v.speaker)
                    .filter(v.voice_name == voice_name)
                )

            result = db.session.execute(stmt).scalar_one_or_none()
            return result
        except Exception as e:
            db.session.rollback()
            raise e

    def get_languages(self) -> List[Dict[str, Any]]:
        """获取所有支持的语言
        
        Returns:
            语言列表
        """
        try:
            stmt = select(GkVoiceLanguage)
            result = db.session.execute(stmt).scalars().all()
            return [item.to_dict() for item in result]
        except Exception as e:
            db.session.rollback()
            raise e

    def get_providers(self) -> List[str]:
        """获取所有供应商
        
        Returns:
            供应商列表
        """
        try:
            stmt = select(GkVoice.provider).distinct()
            result = db.session.execute(stmt).scalars().all()
            return [provider for provider in result if provider]
        except Exception as e:
            db.session.rollback()
            raise e

    def get_voice_by_provider(self, provider: str) -> List[Dict[str, Any]]:
        """根据供应商获取声音
        
        Args:
            provider: 供应商名称
            
        Returns:
            声音列表
        """
        try:
            stmt = select(GkVoice).filter(GkVoice.provider == provider)
            result = db.session.execute(stmt).scalars().all()
            return [item.to_dict() for item in result]
        except Exception as e:
            db.session.rollback()
            raise e

    def get_voice_by_language(self, language: str) -> List[Dict[str, Any]]:
        """根据语言获取声音
        
        Args:
            language: 语言代码
            
        Returns:
            声音列表
        """
        try:
            # 创建别名
            v = aliased(GkVoice)
            lr = aliased(GkVoiceLanguageRelations)

            # 构建查询
            stmt = (
                select(v)
                .join(lr, v.id == lr.voice_id)
                .filter(lr.language == language)
            )

            result = db.session.execute(stmt).scalars().all()
            return [item.to_dict() for item in result]
        except Exception as e:
            db.session.rollback()
            raise e

    def get_provider_by_voice(self, voice_name: str) -> Optional[str]:
        """通过声音ID获取提供商
        
        Args:
            voice_id: 声音ID
            
        Returns:
            提供商名称，如果未找到则返回None
        """
        try:
            stmt = select(GkVoice.provider).filter(GkVoice.voice_name == voice_name)
            result = db.session.execute(stmt).scalar_one_or_none()
            return result
        except Exception as e:
            db.session.rollback()
            raise e
