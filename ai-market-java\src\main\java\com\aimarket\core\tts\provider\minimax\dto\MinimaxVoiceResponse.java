package com.aimarket.core.tts.provider.minimax.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * Minimax 音色列表响应
 * 
 * <AUTHOR> Market Team
 */
public class MinimaxVoiceResponse {

    @JsonProperty("system_voice")
    private List<SystemVoice> systemVoice;
    
    @JsonProperty("voice_cloning")
    private List<VoiceCloning> voiceCloning;
    
    @JsonProperty("voice_generation")
    private List<VoiceGeneration> voiceGeneration;
    
    @JsonProperty("music_generation")
    private List<MusicGeneration> musicGeneration;
    
    @JsonProperty("base_resp")
    private BaseResp baseResp;

    // Constructors
    public MinimaxVoiceResponse() {
    }

    // Getters and Setters
    public List<SystemVoice> getSystemVoice() {
        return systemVoice;
    }

    public void setSystemVoice(List<SystemVoice> systemVoice) {
        this.systemVoice = systemVoice;
    }

    public List<VoiceCloning> getVoiceCloning() {
        return voiceCloning;
    }

    public void setVoiceCloning(List<VoiceCloning> voiceCloning) {
        this.voiceCloning = voiceCloning;
    }

    public List<VoiceGeneration> getVoiceGeneration() {
        return voiceGeneration;
    }

    public void setVoiceGeneration(List<VoiceGeneration> voiceGeneration) {
        this.voiceGeneration = voiceGeneration;
    }

    public List<MusicGeneration> getMusicGeneration() {
        return musicGeneration;
    }

    public void setMusicGeneration(List<MusicGeneration> musicGeneration) {
        this.musicGeneration = musicGeneration;
    }

    public BaseResp getBaseResp() {
        return baseResp;
    }

    public void setBaseResp(BaseResp baseResp) {
        this.baseResp = baseResp;
    }

    /**
     * 系统预定义音色
     */
    public static class SystemVoice {
        @JsonProperty("voice_id")
        private String voiceId;
        
        @JsonProperty("voice_name")
        private String voiceName;
        
        private List<String> description;

        // Getters and Setters
        public String getVoiceId() {
            return voiceId;
        }

        public void setVoiceId(String voiceId) {
            this.voiceId = voiceId;
        }

        public String getVoiceName() {
            return voiceName;
        }

        public void setVoiceName(String voiceName) {
            this.voiceName = voiceName;
        }

        public List<String> getDescription() {
            return description;
        }

        public void setDescription(List<String> description) {
            this.description = description;
        }
    }

    /**
     * 快速复刻音色
     */
    public static class VoiceCloning {
        @JsonProperty("voice_id")
        private String voiceId;
        
        private List<String> description;
        
        @JsonProperty("created_time")
        private String createdTime;

        // Getters and Setters
        public String getVoiceId() {
            return voiceId;
        }

        public void setVoiceId(String voiceId) {
            this.voiceId = voiceId;
        }

        public List<String> getDescription() {
            return description;
        }

        public void setDescription(List<String> description) {
            this.description = description;
        }

        public String getCreatedTime() {
            return createdTime;
        }

        public void setCreatedTime(String createdTime) {
            this.createdTime = createdTime;
        }
    }

    /**
     * 音色生成接口产生的音色
     */
    public static class VoiceGeneration {
        @JsonProperty("voice_id")
        private String voiceId;
        
        private List<String> description;
        
        @JsonProperty("created_time")
        private String createdTime;

        // Getters and Setters
        public String getVoiceId() {
            return voiceId;
        }

        public void setVoiceId(String voiceId) {
            this.voiceId = voiceId;
        }

        public List<String> getDescription() {
            return description;
        }

        public void setDescription(List<String> description) {
            this.description = description;
        }

        public String getCreatedTime() {
            return createdTime;
        }

        public void setCreatedTime(String createdTime) {
            this.createdTime = createdTime;
        }
    }

    /**
     * 音乐生成接口产生的音色
     */
    public static class MusicGeneration {
        @JsonProperty("voice_id")
        private String voiceId;
        
        @JsonProperty("instrumental_id")
        private String instrumentalId;
        
        @JsonProperty("created_time")
        private String createdTime;

        // Getters and Setters
        public String getVoiceId() {
            return voiceId;
        }

        public void setVoiceId(String voiceId) {
            this.voiceId = voiceId;
        }

        public String getInstrumentalId() {
            return instrumentalId;
        }

        public void setInstrumentalId(String instrumentalId) {
            this.instrumentalId = instrumentalId;
        }

        public String getCreatedTime() {
            return createdTime;
        }

        public void setCreatedTime(String createdTime) {
            this.createdTime = createdTime;
        }
    }

    /**
     * 基础响应
     */
    public static class BaseResp {
        @JsonProperty("status_code")
        private Integer statusCode;
        
        @JsonProperty("status_msg")
        private String statusMsg;

        // Getters and Setters
        public Integer getStatusCode() {
            return statusCode;
        }

        public void setStatusCode(Integer statusCode) {
            this.statusCode = statusCode;
        }

        public String getStatusMsg() {
            return statusMsg;
        }

        public void setStatusMsg(String statusMsg) {
            this.statusMsg = statusMsg;
        }
    }
}
