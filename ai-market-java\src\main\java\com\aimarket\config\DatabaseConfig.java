package com.aimarket.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * 数据库配置类
 * 
 * <AUTHOR> Market Team
 */
@Configuration
@EnableJpaRepositories(basePackages = "com.aimarket.repository")
@EnableJpaAuditing
@EnableTransactionManagement
public class DatabaseConfig {
    
    // JPA配置已在application.yml中完成
    // 这里可以添加自定义的数据库配置
}
