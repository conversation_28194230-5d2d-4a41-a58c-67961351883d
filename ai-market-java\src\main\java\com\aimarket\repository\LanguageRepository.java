package com.aimarket.repository;

import com.aimarket.entity.Language;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 语言数据访问层
 * 
 * <AUTHOR> Market Team
 */
@Repository
public interface LanguageRepository extends JpaRepository<Language, Long> {

    /**
     * 根据语言代码查找语言
     */
    Optional<Language> findByCode(String code);

    /**
     * 根据语言名称查找语言
     */
    Optional<Language> findByName(String name);

    /**
     * 检查语言代码是否存在
     */
    boolean existsByCode(String code);

    /**
     * 查找活跃语言
     */
    List<Language> findByIsActiveTrueOrderBySortOrderAsc();

    /**
     * 分页查找活跃语言
     */
    Page<Language> findByIsActiveTrue(Pageable pageable);

    /**
     * 根据名称模糊查询语言
     */
    @Query("SELECT l FROM Language l WHERE l.name LIKE %:name% OR l.englishName LIKE %:name%")
    List<Language> findByNameLike(@Param("name") String name);

    /**
     * 查找有音色的语言
     */
    @Query("SELECT DISTINCT l FROM Language l JOIN l.voices v WHERE v.isActive = true AND l.isActive = true ORDER BY l.sortOrder")
    List<Language> findLanguagesWithActiveVoices();

    /**
     * 统计活跃语言数量
     */
    @Query("SELECT COUNT(l) FROM Language l WHERE l.isActive = true")
    long countActiveLanguages();
}
