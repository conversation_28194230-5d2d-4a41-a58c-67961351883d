"""声音音色、语言关联表模型"""

from sqlalchemy import String, Integer, ForeignKey
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.models.base import BaseModel


class GkVoiceLanguageRelations(BaseModel):
    """声音音色、语言关联表"""

    __tablename__ = 'gk_voice_language_relations'

    id: Mapped[int] = mapped_column(
        Integer,
        primary_key=True,
        autoincrement=True,  # 自动递增，与SQL定义保持一致
        comment='主键ID'
    )
    voice_id: Mapped[int] = mapped_column(
        Integer,
        nullable=True,
        comment='声音id'
    )
    language: Mapped[str] = mapped_column(
        String(255),
        nullable=True,
        comment='语言'
    )
    status: Mapped[int] = mapped_column(
        Integer,
        nullable=True,
        comment='状态'
    )

    def __repr__(self) -> str:
        return f"<GkVoiceLanguageRelations {self.id}: voice_id={self.voice_id}, language={self.language}>"
