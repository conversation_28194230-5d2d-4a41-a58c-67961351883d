package com.aimarket.core.tts.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

import java.util.Map;

/**
 * TTS请求参数
 * 
 * <AUTHOR> Market Team
 */
public class TtsRequest {

    @NotBlank(message = "文本内容不能为空")
    @Size(max = 10000, message = "文本长度不能超过10000个字符")
    private String text;

    @NotBlank(message = "音色ID不能为空")
    private String voiceId;

    private String audioFormat = "mp3";
    private Float speed = 1.0f;
    private Float volume = 1.0f;
    private Float pitch = 1.0f;
    private String style;
    private Boolean streaming = false;
    private Boolean subtitle = false;
    private String outputFormat = "hex"; // hex, url
    
    // 提供商特定的选项
    private Map<String, Object> providerOptions;

    // Constructors
    public TtsRequest() {
    }

    public TtsRequest(String text, String voiceId) {
        this.text = text;
        this.voiceId = voiceId;
    }

    // Getters and Setters
    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getVoiceId() {
        return voiceId;
    }

    public void setVoiceId(String voiceId) {
        this.voiceId = voiceId;
    }

    public String getAudioFormat() {
        return audioFormat;
    }

    public void setAudioFormat(String audioFormat) {
        this.audioFormat = audioFormat;
    }

    public Float getSpeed() {
        return speed;
    }

    public void setSpeed(Float speed) {
        this.speed = speed;
    }

    public Float getVolume() {
        return volume;
    }

    public void setVolume(Float volume) {
        this.volume = volume;
    }

    public Float getPitch() {
        return pitch;
    }

    public void setPitch(Float pitch) {
        this.pitch = pitch;
    }

    public String getStyle() {
        return style;
    }

    public void setStyle(String style) {
        this.style = style;
    }

    public Boolean getStreaming() {
        return streaming;
    }

    public void setStreaming(Boolean streaming) {
        this.streaming = streaming;
    }

    public Boolean getSubtitle() {
        return subtitle;
    }

    public void setSubtitle(Boolean subtitle) {
        this.subtitle = subtitle;
    }

    public String getOutputFormat() {
        return outputFormat;
    }

    public void setOutputFormat(String outputFormat) {
        this.outputFormat = outputFormat;
    }

    public Map<String, Object> getProviderOptions() {
        return providerOptions;
    }

    public void setProviderOptions(Map<String, Object> providerOptions) {
        this.providerOptions = providerOptions;
    }

    @Override
    public String toString() {
        return "TtsRequest{" +
                "text='" + (text != null ? text.substring(0, Math.min(text.length(), 50)) + "..." : null) + '\'' +
                ", voiceId='" + voiceId + '\'' +
                ", audioFormat='" + audioFormat + '\'' +
                ", speed=" + speed +
                ", volume=" + volume +
                ", pitch=" + pitch +
                ", style='" + style + '\'' +
                ", streaming=" + streaming +
                ", subtitle=" + subtitle +
                ", outputFormat='" + outputFormat + '\'' +
                '}';
    }
}
