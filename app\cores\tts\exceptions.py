from typing import Optional

class TTSException(Exception):
    """TTS基础异常类"""
    def __init__(self, message: str, provider: str, status_code: Optional[int] = None):
        self.message = message
        self.provider = provider
        self.status_code = status_code
        super().__init__(f"[{provider}] {message}")

class TTSProviderException(TTSException):
    """TTS提供商异常"""
    pass

class TTSConfigException(TTSException):
    """TTS配置异常"""
    pass
