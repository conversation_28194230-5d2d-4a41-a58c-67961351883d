package com.aimarket.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

import java.math.BigDecimal;

/**
 * 产品实体类
 * 用于演示模块
 * 
 * <AUTHOR> Market Team
 */
@Entity
@Table(name = "products", indexes = {
    @Index(name = "idx_product_name", columnList = "name"),
    @Index(name = "idx_product_status", columnList = "status"),
    @Index(name = "idx_product_category", columnList = "category")
})
public class Product extends BaseEntity {

    @NotBlank(message = "产品名称不能为空")
    @Size(max = 100, message = "产品名称长度不能超过100个字符")
    @Column(name = "name", length = 100, nullable = false)
    private String name;

    @Size(max = 500, message = "产品描述长度不能超过500个字符")
    @Column(name = "description", length = 500)
    private String description;

    @DecimalMin(value = "0.0", inclusive = false, message = "价格必须大于0")
    @Column(name = "price", precision = 10, scale = 2, nullable = false)
    private BigDecimal price;

    @Min(value = 0, message = "库存不能为负数")
    @Column(name = "stock", nullable = false)
    private Integer stock = 0;

    @Size(max = 50, message = "分类长度不能超过50个字符")
    @Column(name = "category", length = 50)
    private String category;

    @Size(max = 20, message = "状态长度不能超过20个字符")
    @Column(name = "status", length = 20, nullable = false)
    private String status = "active";

    @Size(max = 255, message = "图片URL长度不能超过255个字符")
    @Column(name = "image_url")
    private String imageUrl;

    @Size(max = 200, message = "标签长度不能超过200个字符")
    @Column(name = "tags", length = 200)
    private String tags;

    @Column(name = "is_featured", nullable = false)
    private Boolean isFeatured = false;

    @Column(name = "view_count", nullable = false)
    private Long viewCount = 0L;

    @Column(name = "sale_count", nullable = false)
    private Long saleCount = 0L;

    // Constructors
    public Product() {
    }

    public Product(String name, BigDecimal price, Integer stock) {
        this.name = name;
        this.price = price;
        this.stock = stock;
    }

    // Getters and Setters
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public Integer getStock() {
        return stock;
    }

    public void setStock(Integer stock) {
        this.stock = stock;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    public Boolean getIsFeatured() {
        return isFeatured;
    }

    public void setIsFeatured(Boolean isFeatured) {
        this.isFeatured = isFeatured;
    }

    public Long getViewCount() {
        return viewCount;
    }

    public void setViewCount(Long viewCount) {
        this.viewCount = viewCount;
    }

    public Long getSaleCount() {
        return saleCount;
    }

    public void setSaleCount(Long saleCount) {
        this.saleCount = saleCount;
    }

    // 便利方法
    public void incrementViewCount() {
        this.viewCount++;
    }

    public void incrementSaleCount() {
        this.saleCount++;
    }

    public boolean isInStock() {
        return stock > 0;
    }

    public boolean isActive() {
        return "active".equals(status);
    }

    @Override
    public String toString() {
        return "Product{" +
                "id=" + getId() +
                ", name='" + name + '\'' +
                ", price=" + price +
                ", stock=" + stock +
                ", category='" + category + '\'' +
                ", status='" + status + '\'' +
                ", isFeatured=" + isFeatured +
                ", viewCount=" + viewCount +
                ", saleCount=" + saleCount +
                ", createdAt=" + getCreatedAt() +
                ", updatedAt=" + getUpdatedAt() +
                '}';
    }
}
