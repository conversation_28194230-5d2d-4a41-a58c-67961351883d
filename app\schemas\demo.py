from marshmallow import fields, validate, validates, validates_schema, ValidationError, EXCLUDE
from typing import Dict, Any
from app.schemas.base import BaseSchema

class UserSchema(BaseSchema):
    """用户Schema"""
    
    username = fields.String(
        required=True,
        validate=validate.Length(min=3, max=50),
        error_messages={
            "required": "用户名不能为空",
            "validator_failed": "用户名长度必须在3-50个字符之间"
        }
    )
    email = fields.Email(
        required=True,
        error_messages={
            "required": "邮箱不能为空",
            "invalid": "邮箱格式不正确"
        }
    )
    password = fields.String(
        required=True,
        validate=validate.Length(min=6, max=128),
        load_only=True,
        error_messages={
            "required": "密码不能为空",
            "validator_failed": "密码长度必须在6-128个字符之间"
        }
    )
    nickname = fields.String(
        validate=validate.Length(max=50),
        allow_none=True,
        error_messages={
            "validator_failed": "昵称长度不能超过50个字符"
        }
    )

class UserCreateSchema(UserSchema):
    """用户创建Schema"""
    
    password_confirm = fields.String(
        required=True,
        load_only=True,
        error_messages={
            "required": "确认密码不能为空"
        }
    )

    @validates("password_confirm")
    def validate_password_confirm(self, value):
        """验证确认密码
        Args:
            value: 确认密码
        Raises:
            ValidationError: 验证失败
        """
        if value != self.context.get("password"):
            raise ValidationError("两次输入的密码不一致")

class UserUpdateSchema(BaseSchema):
    """用户更新Schema"""
    
    nickname = fields.String(
        validate=validate.Length(max=50),
        allow_none=True,
        error_messages={
            "validator_failed": "昵称长度不能超过50个字符"
        }
    )
    password = fields.String(
        validate=validate.Length(min=6, max=128),
        load_only=True,
        error_messages={
            "validator_failed": "密码长度必须在6-128个字符之间"
        }
    )

class CategorySchema(BaseSchema):
    """商品分类Schema"""
    
    name = fields.String(
        required=True,
        validate=validate.Length(min=1, max=50),
        error_messages={
            "required": "分类名称不能为空",
            "validator_failed": "分类名称长度必须在1-50个字符之间"
        }
    )
    description = fields.String(
        validate=validate.Length(max=200),
        allow_none=True,
        error_messages={
            "validator_failed": "分类描述长度不能超过200个字符"
        }
    )

class ProductSchema(BaseSchema):
    """商品Schema"""
    class Meta:
        unknown = EXCLUDE  # 忽略未知字段
    
    name = fields.String(
        required=True,
        validate=validate.Length(min=1, max=100),
        error_messages={
            "required": "商品名称不能为空",
            "validator_failed": "商品名称长度必须在1-100个字符之间"
        }
    )
    price = fields.Float(
        required=True,
        validate=validate.Range(min=0.01),
        error_messages={
            "required": "商品价格不能为空",
            "validator_failed": "商品价格必须大于0.01",
            "invalid": "商品价格必须是数字"
        }
    )
    stock = fields.Integer(
        required=True,
        validate=validate.Range(min=0),
        error_messages={
            "required": "商品库存不能为空",
            "validator_failed": "商品库存不能小于0",
            "invalid": "商品库存必须是整数"
        }
    )
    category = fields.Nested(
        CategorySchema,
        required=True,
        error_messages={
            "required": "商品分类不能为空"
        }
    )
    tags = fields.List(
        fields.String(validate=validate.Length(min=1, max=20)),
        validate=validate.Length(max=5),
        error_messages={
            "validator_failed": "标签数量不能超过5个"
        }
    )
    status = fields.String(
        validate=validate.OneOf(['active', 'inactive', 'deleted']),
        default='active',
        error_messages={
            "validator_failed": "商品状态必须是 active、inactive 或 deleted"
        }
    )

    @validates('tags')
    def validate_tags(self, value):
        """验证标签"""
        if value and len(set(value)) != len(value):
            raise ValidationError("标签不能重复")
        
        for tag in value:
            if not tag.strip():
                raise ValidationError("标签不能为空")

    @validates_schema
    def validate_stock_status(self, data: Dict[str, Any], **kwargs):
        """验证库存和状态的关系"""
        if data.get('status') == 'active' and data.get('stock', 0) <= 0:
            raise ValidationError("活动状态的商品库存必须大于0")

class ProductQuerySchema(BaseSchema):
    """商品查询Schema"""
    
    name = fields.String(
        validate=validate.Length(max=100),
        error_messages={
            "validator_failed": "商品名称长度不能超过100个字符"
        }
    )
    min_price = fields.Float(
        validate=validate.Range(min=0),
        error_messages={
            "validator_failed": "最小价格不能小于0",
            "invalid": "最小价格必须是数字"
        }
    )
    max_price = fields.Float(
        validate=validate.Range(min=0),
        error_messages={
            "validator_failed": "最大价格不能小于0",
            "invalid": "最大价格必须是数字"
        }
    )
    category_name = fields.String(
        validate=validate.Length(max=50),
        error_messages={
            "validator_failed": "分类名称长度不能超过50个字符"
        }
    )
    status = fields.String(
        validate=validate.OneOf(['active', 'inactive', 'deleted', '']),
        error_messages={
            "validator_failed": "商品状态必须是 active、inactive 或 deleted"
        }
    )
    page = fields.Integer(
        validate=validate.Range(min=1),
        missing=1,
        error_messages={
            "validator_failed": "页码必须大于等于1",
            "invalid": "页码必须是整数"
        }
    )
    size = fields.Integer(
        validate=validate.Range(min=1, max=100),
        missing=10,
        error_messages={
            "validator_failed": "每页数量必须在1-100之间",
            "invalid": "每页数量必须是整数"
        }
    )

    @validates_schema
    def validate_price_range(self, data: Dict[str, Any], **kwargs):
        """验证价格区间"""
        min_price = data.get('min_price')
        max_price = data.get('max_price')
        if min_price is not None and max_price is not None:
            if min_price > max_price:
                raise ValidationError("最小价格不能大于最大价格")
