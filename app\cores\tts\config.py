from dataclasses import dataclass
from typing import Dict, List
import os
from dotenv import load_dotenv

@dataclass
class TTSProviderConfig:
    """TTS提供商配置"""
    name: str
    keys: List[Dict[str, str]]

class TTSConfigManager:
    """TTS配置管理器"""
    
    def __init__(self):
        """初始化配置管理器"""
        self._initialize()
    
    def _initialize(self):
        """初始化配置"""
        # 加载环境变量
        env_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'api_key.env')
        load_dotenv(env_path)
        
        # 初始化提供商配置
        self._providers = {
            "minimax": TTSProviderConfig(
                name="minimax",
                keys=[{
                    "api_key": os.getenv('minimax_api_key'),
                    "group_id": os.getenv('minimax_group_id')
                }]
            ),
            "moyin": TTSProviderConfig(
                name="moyin",
                keys=[{
                    "appkey": os.getenv('moyin_app_key'),
                    "secret": os.getenv('moyin_secret')
                }]
            )
        }
    
    @property
    def providers(self) -> Dict[str, TTSProviderConfig]:
        """获取所有提供商配置"""
        return self._providers
    
    def get_provider(self, name: str) -> TTSProviderConfig:
        """获取指定提供商配置"""
        return self._providers.get(name)
