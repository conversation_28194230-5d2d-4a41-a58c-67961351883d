from typing import Any
from flask_restful import Resource
from app.schemas.base import QuerySchema
from app.services.demo import image_service
from flask import request, current_app
from flask_restful import Resource
from app.utils.response import APIResponse
from marshmallow import ValidationError
class ImageAPI(Resource):
    """图片资源API"""

    def get(self, image_id: int | None = None) -> Any:
        """获取图片资源

        Args:
            image_id: 图片ID，如果为None则获取列表
        """
        try:
            if image_id is None:
                # 获取列表
                query_schema = QuerySchema()
                params = query_schema.load(request.args)

                result = image_service.get_list(
                    page=params['page'],
                    per_page=params['per_page'],
                    only_active=params['only_active']
                )
            else:
                # 获取单个资源
                result = image_service.get_by_id(image_id)

            return APIResponse.success(data=result)
        except ValidationError as e:
            current_app.logger.error(f"Validation error: {str(e.messages)}")
            return APIResponse.error(message=str(e.messages))
        except Exception as e:
            current_app.logger.error(f"Error in get image: {str(e)}")
            return APIResponse.error(message=str(e))

    def post(self) -> Any:
        """创建图片资源"""
        try:
            result = image_service.create(request.json)
            return APIResponse.success(data=result)
        except ValidationError as e:
            current_app.logger.error(f"Validation error: {str(e.messages)}")
            return APIResponse.error(message=str(e.messages))
        except Exception as e:
            current_app.logger.error(f"Error in create image: {str(e)}")
            return APIResponse.error(message=str(e))


    def put(self, image_id: int) -> Any:
        """更新图片资源"""
        try:
            result = image_service.update(image_id, request.json)
            return APIResponse.success(data=result)
        except ValidationError as e:
            current_app.logger.error(f"Validation error: {str(e.messages)}")
            return APIResponse.error(message=str(e.messages))
        except Exception as e:
            current_app.logger.error(f"Error in update image: {str(e)}")
            return APIResponse.error(message=str(e))

    def delete(self, image_id: int) -> Any:
        """删除图片资源"""
        try:
            result = image_service.delete(image_id)
            return APIResponse.success(data={'success': result})
        except Exception as e:
            current_app.logger.error(f"Error in delete image: {str(e)}")
            return APIResponse.error(message=str(e))