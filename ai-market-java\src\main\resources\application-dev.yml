# 开发环境配置
spring:
  # 数据源配置
  datasource:
    url: *******************************************************************************************************************************************************
    username: root
    password: root123
  
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        format_sql: true
  
  # Redis配置
  data:
    redis:
      host: localhost
      port: 6379
      password: 
      database: 0

# 日志配置
logging:
  level:
    com.aimarket: DEBUG
    org.springframework.web: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  
# 开发环境特定配置
aimarket:
  # 音频文件存储路径
  audio:
    storage-path: ./temp/audio
    url-prefix: http://localhost:8080/ai/market/audio/files
  
  # 图像存储路径
  image:
    storage-path: ./temp/images
