from typing import Any, Dict, List, Optional, Type, TypeVar, Union
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy import select, exists
from app.extensions import db
from app.models.base import BaseModel
from app.utils.exceptions import BusinessError

T = TypeVar('T', bound=BaseModel)

class BaseRepository:
    """仓储基类"""

    def __init__(self, model: Type[T]):
        """初始化
        Args:
            model: 模型类
        """
        self.model = model

    def get_by_id(self, id_: int) -> Optional[T]:
        """根据ID获取记录
        Args:
            id_: 记录ID
        Returns:
            记录实例或None
        """
        try:
            return db.session.get(self.model, id_)
        except SQLAlchemyError as e:
            db.session.rollback()
            raise BusinessError(f"Database error: {str(e)}")

    def get_all(
        self,
        page: int = 1,
        per_page: int = 10,
        only_active: bool = True
    ) -> Dict[str, Any]:
        """获取所有记录
        Args:
            page: 页码
            per_page: 每页数量
            only_active: 是否只返回未删除的记录
        Returns:
            包含分页信息的字典
        """
        try:
            # 构建查询
            stmt = select(self.model)
            if only_active:
                stmt = stmt.filter_by(is_deleted=False)
            
            # 执行分页查询
            pagination = db.paginate(
                stmt,
                page=page,
                per_page=per_page
            )
            
            return {
                'items': [item.to_dict() for item in pagination.items],
                'total': pagination.total,
                'page': pagination.page,
                'per_page': pagination.per_page,
                'pages': pagination.pages
            }
        except SQLAlchemyError as e:
            db.session.rollback()
            raise BusinessError(f"Database error: {str(e)}")

    def create(self, data: Dict[str, Any]) -> T:
        """创建记录
        Args:
            data: 记录数据
        Returns:
            创建的记录实例
        """
        try:
            instance = self.model(**data)
            db.session.add(instance)
            db.session.commit()
            return instance
        except SQLAlchemyError as e:
            db.session.rollback()
            raise BusinessError(f"Database error: {str(e)}")

    def update(self, id_: int, data: Dict[str, Any]) -> Optional[T]:
        """更新记录
        Args:
            id_: 记录ID
            data: 更新数据
        Returns:
            更新后的记录实例或None
        """
        try:
            instance = self.get_by_id(id_)
            if not instance:
                return None
            
            for key, value in data.items():
                setattr(instance, key, value)
            
            db.session.commit()
            return instance
        except SQLAlchemyError as e:
            db.session.rollback()
            raise BusinessError(f"Database error: {str(e)}")

    def delete(self, id_: int, hard: bool = False) -> bool:
        """删除记录
        Args:
            id_: 记录ID
            hard: 是否硬删除
        Returns:
            是否删除成功
        """
        try:
            instance = self.get_by_id(id_)
            if not instance:
                return False
            
            if hard:
                db.session.delete(instance)
            else:
                instance.is_deleted = True
            
            db.session.commit()
            return True
        except SQLAlchemyError as e:
            db.session.rollback()
            raise BusinessError(f"Database error: {str(e)}")

    def exists(self, **kwargs) -> bool:
        """检查记录是否存在
        Args:
            **kwargs: 查询条件
        Returns:
            是否存在
        """
        try:
            stmt = exists(select(self.model).filter_by(**kwargs)).select()
            return bool(db.session.scalar(stmt))
        except SQLAlchemyError as e:
            db.session.rollback()
            raise BusinessError(f"Database error: {str(e)}")

    def get_by_filter(
        self,
        filters: Dict[str, Any],
        page: int = 1,
        per_page: int = 10
    ) -> Dict[str, Any]:
        """根据过滤条件获取记录
        Args:
            filters: 过滤条件
            page: 页码
            per_page: 每页数量
        Returns:
            包含分页信息的字典
        """
        try:
            stmt = select(self.model).filter_by(**filters)
            pagination = db.paginate(
                stmt,
                page=page,
                per_page=per_page
            )
            
            return {
                'items': [item.to_dict() for item in pagination.items],
                'total': pagination.total,
                'page': pagination.page,
                'per_page': pagination.per_page,
                'pages': pagination.pages
            }
        except SQLAlchemyError as e:
            db.session.rollback()
            raise BusinessError(f"Database error: {str(e)}")
