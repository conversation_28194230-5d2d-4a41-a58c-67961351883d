from functools import wraps
from flask import current_app
from app.extensions import cache

def cached_by_args(timeout=300):
    """自定义缓存装饰器，基于函数名和参数生成缓存键，忽略self参数
    
    Args:
        timeout: 缓存超时时间（秒）
        
    Returns:
        装饰器函数
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(self, *args, **kwargs):
            # 生成缓存键：函数名+参数（忽略self）
            key_parts = [f.__name__]
            
            # 添加位置参数
            for arg in args:
                key_parts.append(str(arg))
                
            # 添加关键字参数（按键排序）
            for k in sorted(kwargs.keys()):
                key_parts.append(f"{k}:{kwargs[k]}")
                
            cache_key = "custom_cache:" + ":".join(key_parts)
            
            # 尝试从缓存获取
            cached_value = cache.get(cache_key)
            if cached_value is not None:
                current_app.logger.info(f"Cache hit for key: {cache_key}")
                return cached_value
                
            # 缓存未命中，执行原函数
            current_app.logger.info(f"Cache miss for key: {cache_key}")
            result = f(self, *args, **kwargs)
            
            # 存储到缓存
            cache.set(cache_key, result, timeout=timeout)
            
            return result
        return decorated_function
    return decorator
