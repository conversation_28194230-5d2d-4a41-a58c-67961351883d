import os
from typing import Dict, Any, Union, Literal
from app.cores import fetcher
import requests
from .schemas import (
    TTSRequest, TTSResponse, GetVoiceResponse,
    SystemVoice, VoiceCloning, VoiceGeneration, MusicGeneration
)
from .exceptions import MinimaxTTSException, ERROR_MAPPING

class MinimaxTTS:
    """Minimax TTS API 客户端"""
    
    BASE_URL = os.getenv('MINIMAX_BASE_URL',)
    TTS_URL = "/v1/t2a_v2"
    GET_VOICE_URL = "/v1/get_voice"
    
    def __init__(self, api_key: str, group_id: str):
        """
        初始化 Minimax TTS 客户端
        
        Args:
            api_key: API密钥
            group_id: 用户组ID
        """
        self.api_key = api_key
        self.group_id = group_id
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
    
    def _handle_error(self, response_data: Dict[str, Any]) -> None:
        """处理API错误响应"""
        if "base_resp" in response_data and response_data["base_resp"]:
            base_resp = response_data["base_resp"]
            status_code = base_resp.get("status_code")
            status_msg = base_resp.get("status_msg")
            
            if status_code:
                error_info = ERROR_MAPPING.get(status_code, ("APIError", "未知API错误"))
                error_class = error_info[0]
                error_msg = status_msg or error_info[1]
                
                raise MinimaxTTSException(
                    message=f"{error_class}: {error_msg}",
                    status_code=status_code
                )
    
    def text_to_speech(
        self,
        request: Union[TTSRequest, Dict[str, Any]]
    ) -> TTSResponse:
        """
        将文本转换为语音
        
        Args:
            request: TTS请求参数，可以是TTSRequest对象或字典
            
        Returns:
            TTSResponse: TTS响应对象
            
        Raises:
            MinimaxTTSException: API调用出错时抛出
        """
        if isinstance(request, dict):
            request = TTSRequest(**request)

        url = f"{self.BASE_URL}{self.TTS_URL}?GroupId={self.group_id}"
        
        try:
            response = fetcher.post(
                url,
                headers=self.headers,
                json=request.model_dump(exclude_none=True)
            )
            # print("Trace-ID:", response.headers.get("Trace-Id"))

            response.raise_for_status()
            response_data = response.json()

            # 检查错误
            self._handle_error(response_data)
            
            # 解析响应
            return TTSResponse(**response_data)
        except requests.exceptions.HTTPError as e:
            raise MinimaxTTSException(
                message=f"HTTP错误: {str(e)}",
                status_code=e.response.status_code
            )
        except requests.exceptions.RequestException as e:
            raise MinimaxTTSException(
                message=f"请求错误: {str(e)}"
            )
        except Exception as e:
            raise MinimaxTTSException(
                message=f"未知错误: {str(e)}"
            )
    
    def get_voice(
        self,
        voice_type: Literal["system", "voice_cloning", "voice_generation", "music_generation", "all"]
    ) -> GetVoiceResponse:
        """
        查询可用的音色列表
        
        Args:
            voice_type: 音色类型，可选值：
                - "system": 系统音色
                - "voice_cloning": 快速复刻的音色
                - "voice_generation": 文生音色接口生成的音色
                - "music_generation": 音乐生成产生的人声或者伴奏音色
                - "all": 以上全部
                
        Returns:
            GetVoiceResponse: 音色列表响应对象
            
        Raises:
            MinimaxTTSException: API调用出错时抛出
        """
        url = f"{self.BASE_URL}{self.GET_VOICE_URL}"
        
        try:
            response = fetcher.post(
                url,
                headers=self.headers,
                json={"voice_type": voice_type}
            )
            response.raise_for_status()
            response_data = response.json()
            
            # 检查错误
            self._handle_error(response_data)
            
            # 解析响应
            return GetVoiceResponse(**response_data)
                
        except requests.exceptions.HTTPError as e:
            raise MinimaxTTSException(
                message=f"HTTP错误: {str(e)}",
                status_code=e.response.status_code
            )
        except requests.exceptions.RequestException as e:
            raise MinimaxTTSException(
                message=f"请求错误: {str(e)}"
            )
        except Exception as e:
            raise MinimaxTTSException(
                message=f"未知错误: {str(e)}"
            )
