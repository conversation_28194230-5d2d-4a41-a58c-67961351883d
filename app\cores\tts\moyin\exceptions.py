class MoyinTTSException(Exception):
    """Moyin TTS 基础异常类"""
    def __init__(self, message: str, status_code: int = None):
        self.message = message
        self.status_code = status_code
        super().__init__(self.message)

class AuthenticationError(MoyinTTSException):
    """认证错误"""
    pass

class InvalidRequestError(MoyinTTSException):
    """无效请求错误"""
    pass

class RateLimitError(MoyinTTSException):
    """限流错误"""
    pass

class APIError(MoyinTTSException):
    """API 错误"""
    pass

ERROR_MAPPING = {
    400: ("BadRequestError", "请求参数错误"),
    401: ("AuthenticationError", "认证失败"),
    404: ("NotFoundError", "参数信息未找到"),
    408: ("TimeoutError", "请求超时"),
    413: ("TextLengthError", "文本长度超过限制"),
    500: ("ServerError", "服务器错误"),
    502: ("BadGatewayError", "网关错误"),
    503: ("ServiceUnavailableError", "服务不可用"),
    10006: ("ConcurrentError", "并发错误"),
    10007: ("ParameterError", "参数异常"),
    10008: ("VerificationError", "验证失败"),
    10009: ("SignatureError", "签名异常"),
    10010: ("AppKeyError", "AppKey异常"),
    10011: ("MissingParameterError", "缺少必需参数"),
    10012: ("AppNotExistError", "应用不存在"),
    10013: ("ServiceDisabledError", "服务已禁用"),
    10014: ("CertificationExpiredError", "认证已过期"),
    10015: ("AccountFrozenError", "账户冻结"),
    10016: ("UnauthenticatedError", "未认证的请求"),
    10017: ("RefererAuthError", "未设置Referer认证"),
    10018: ("CloneTaskError", "克隆任务未完成"),
    10019: ("UnauthorizedVoiceError", "未授权个人定制发音人"),
    10020: ("CallNameOfflineError", "调用名已下线"),
    10021: ("UserNotExistError", "用户不存在"),
    10022: ("CallLimitError", "调用次数超过限制"),
    10023: ("IPWhitelistError", "不在IP白名单"),
    10024: ("QPSLimitError", "QPS超过限制"),
    10025: ("InitializationError", "初始化失败"),
    10026: ("InsufficientBalanceError", "账户余额不足"),
    31000: ("InvalidTextError", "文本异常"),
    31001: ("TextSizeError", "文本超长"),
    31002: ("ConversionError", "转换失败"),
    31003: ("TTSError", "文本转语音失败"),
    31004: ("InvalidNetTypeError", "无效的网络类型"),
    31005: ("InvalidSpeakerAuthError", "无效的发音人认证"),
    31006: ("InvalidAudioTypeError", "无效的音频格式"),
    31007: ("InvalidRateError", "无效的采样率"),
    31008: ("InvalidSpeedError", "无效的语速"),
    31009: ("InvalidPitchError", "无效的音调"),
    31010: ("InvalidSymbolSilError", "无效的停顿符号"),
    40080: ("SpeakerNotExistError", "发音人不存在"),
    40081: ("LanguageMismatchError", "语言不匹配"),
    40084: ("SensitiveContentError", "包含敏感信息")
}
