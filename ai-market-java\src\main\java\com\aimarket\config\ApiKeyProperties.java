package com.aimarket.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * API密钥配置属性
 * 
 * <AUTHOR> Market Team
 */
@Component
@ConfigurationProperties
@PropertySource("classpath:api-keys.properties")
public class ApiKeyProperties {

    private Minimax minimax = new Minimax();
    private Moyin moyin = new Moyin();

    // Getters and Setters
    public Minimax getMinimax() {
        return minimax;
    }

    public void setMinimax(Minimax minimax) {
        this.minimax = minimax;
    }

    public Moyin getMoyin() {
        return moyin;
    }

    public void setMoyin(<PERSON>yin moyin) {
        this.moyin = moyin;
    }

    /**
     * Minimax API配置
     */
    public static class Minimax {
        private Api api = new Api();

        public Api getApi() {
            return api;
        }

        public void setApi(Api api) {
            this.api = api;
        }

        public static class Api {
            private List<String> keys;
            private String baseUrl;
            private String groupId;
            private String defaultModel;

            // Getters and Setters
            public List<String> getKeys() {
                return keys;
            }

            public void setKeys(List<String> keys) {
                this.keys = keys;
            }

            public String getBaseUrl() {
                return baseUrl;
            }

            public void setBaseUrl(String baseUrl) {
                this.baseUrl = baseUrl;
            }

            public String getGroupId() {
                return groupId;
            }

            public void setGroupId(String groupId) {
                this.groupId = groupId;
            }

            public String getDefaultModel() {
                return defaultModel;
            }

            public void setDefaultModel(String defaultModel) {
                this.defaultModel = defaultModel;
            }
        }
    }

    /**
     * Moyin API配置
     */
    public static class Moyin {
        private Api api = new Api();

        public Api getApi() {
            return api;
        }

        public void setApi(Api api) {
            this.api = api;
        }

        public static class Api {
            private List<String> keys;
            private String baseUrl;
            private String appId;
            private String appSecret;

            // Getters and Setters
            public List<String> getKeys() {
                return keys;
            }

            public void setKeys(List<String> keys) {
                this.keys = keys;
            }

            public String getBaseUrl() {
                return baseUrl;
            }

            public void setBaseUrl(String baseUrl) {
                this.baseUrl = baseUrl;
            }

            public String getAppId() {
                return appId;
            }

            public void setAppId(String appId) {
                this.appId = appId;
            }

            public String getAppSecret() {
                return appSecret;
            }

            public void setAppSecret(String appSecret) {
                this.appSecret = appSecret;
            }
        }
    }
}
