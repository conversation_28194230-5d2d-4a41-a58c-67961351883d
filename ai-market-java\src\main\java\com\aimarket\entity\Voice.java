package com.aimarket.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

import java.math.BigDecimal;

/**
 * 音色实体类
 * 对应Python模型中的Voice类
 * 
 * <AUTHOR> Market Team
 */
@Entity
@Table(name = "voices", indexes = {
    @Index(name = "idx_voice_id", columnList = "voice_id"),
    @Index(name = "idx_provider", columnList = "provider"),
    @Index(name = "idx_language_id", columnList = "language_id"),
    @Index(name = "idx_gender", columnList = "gender")
})
public class Voice extends BaseEntity {

    @NotBlank(message = "音色ID不能为空")
    @Size(max = 100, message = "音色ID长度不能超过100个字符")
    @Column(name = "voice_id", length = 100, nullable = false)
    private String voiceId;

    @NotBlank(message = "音色名称不能为空")
    @Size(max = 100, message = "音色名称长度不能超过100个字符")
    @Column(name = "name", length = 100, nullable = false)
    private String name;

    @NotBlank(message = "提供商不能为空")
    @Size(max = 50, message = "提供商长度不能超过50个字符")
    @Column(name = "provider", length = 50, nullable = false)
    private String provider;

    @Size(max = 20, message = "性别长度不能超过20个字符")
    @Column(name = "gender", length = 20)
    private String gender;

    @Size(max = 20, message = "年龄段长度不能超过20个字符")
    @Column(name = "age_group", length = 20)
    private String ageGroup;

    @Size(max = 50, message = "风格长度不能超过50个字符")
    @Column(name = "style", length = 50)
    private String style;

    @Column(name = "price", precision = 10, scale = 4)
    private BigDecimal price;

    @Size(max = 10, message = "货币单位长度不能超过10个字符")
    @Column(name = "currency", length = 10)
    private String currency = "CNY";

    @Column(name = "is_active", nullable = false)
    private Boolean isActive = true;

    @Column(name = "sort_order", nullable = false)
    private Integer sortOrder = 0;

    @Size(max = 500, message = "描述长度不能超过500个字符")
    @Column(name = "description", length = 500)
    private String description;

    @Size(max = 255, message = "示例音频URL长度不能超过255个字符")
    @Column(name = "sample_audio_url")
    private String sampleAudioUrl;

    @Column(name = "is_premium", nullable = false)
    private Boolean isPremium = false;

    @Size(max = 100, message = "标签长度不能超过100个字符")
    @Column(name = "tags", length = 100)
    private String tags;

    // 与Language的关联关系
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "language_id", nullable = false)
    private Language language;

    // Constructors
    public Voice() {
    }

    public Voice(String voiceId, String name, String provider) {
        this.voiceId = voiceId;
        this.name = name;
        this.provider = provider;
    }

    // Getters and Setters
    public String getVoiceId() {
        return voiceId;
    }

    public void setVoiceId(String voiceId) {
        this.voiceId = voiceId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getProvider() {
        return provider;
    }

    public void setProvider(String provider) {
        this.provider = provider;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getAgeGroup() {
        return ageGroup;
    }

    public void setAgeGroup(String ageGroup) {
        this.ageGroup = ageGroup;
    }

    public String getStyle() {
        return style;
    }

    public void setStyle(String style) {
        this.style = style;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public Boolean getIsActive() {
        return isActive;
    }

    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getSampleAudioUrl() {
        return sampleAudioUrl;
    }

    public void setSampleAudioUrl(String sampleAudioUrl) {
        this.sampleAudioUrl = sampleAudioUrl;
    }

    public Boolean getIsPremium() {
        return isPremium;
    }

    public void setIsPremium(Boolean isPremium) {
        this.isPremium = isPremium;
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    public Language getLanguage() {
        return language;
    }

    public void setLanguage(Language language) {
        this.language = language;
    }

    @Override
    public String toString() {
        return "Voice{" +
                "id=" + getId() +
                ", voiceId='" + voiceId + '\'' +
                ", name='" + name + '\'' +
                ", provider='" + provider + '\'' +
                ", gender='" + gender + '\'' +
                ", style='" + style + '\'' +
                ", price=" + price +
                ", isActive=" + isActive +
                ", createdAt=" + getCreatedAt() +
                ", updatedAt=" + getUpdatedAt() +
                '}';
    }
}
