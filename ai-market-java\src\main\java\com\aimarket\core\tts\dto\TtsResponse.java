package com.aimarket.core.tts.dto;

import java.util.Map;

/**
 * TTS响应结果
 * 
 * <AUTHOR> Market Team
 */
public class TtsResponse {

    private String audioData;
    private String subtitleUrl;
    private String audioFormat;
    private Long audioLength;
    private Integer audioSampleRate;
    private Long audioSize;
    private Integer bitrate;
    private Integer audioChannel;
    private String traceId;
    private Map<String, Object> extraInfo;

    // Constructors
    public TtsResponse() {
    }

    public TtsResponse(String audioData, String audioFormat) {
        this.audioData = audioData;
        this.audioFormat = audioFormat;
    }

    // Getters and Setters
    public String getAudioData() {
        return audioData;
    }

    public void setAudioData(String audioData) {
        this.audioData = audioData;
    }

    public String getSubtitleUrl() {
        return subtitleUrl;
    }

    public void setSubtitleUrl(String subtitleUrl) {
        this.subtitleUrl = subtitleUrl;
    }

    public String getAudioFormat() {
        return audioFormat;
    }

    public void setAudioFormat(String audioFormat) {
        this.audioFormat = audioFormat;
    }

    public Long getAudioLength() {
        return audioLength;
    }

    public void setAudioLength(Long audioLength) {
        this.audioLength = audioLength;
    }

    public Integer getAudioSampleRate() {
        return audioSampleRate;
    }

    public void setAudioSampleRate(Integer audioSampleRate) {
        this.audioSampleRate = audioSampleRate;
    }

    public Long getAudioSize() {
        return audioSize;
    }

    public void setAudioSize(Long audioSize) {
        this.audioSize = audioSize;
    }

    public Integer getBitrate() {
        return bitrate;
    }

    public void setBitrate(Integer bitrate) {
        this.bitrate = bitrate;
    }

    public Integer getAudioChannel() {
        return audioChannel;
    }

    public void setAudioChannel(Integer audioChannel) {
        this.audioChannel = audioChannel;
    }

    public String getTraceId() {
        return traceId;
    }

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }

    public Map<String, Object> getExtraInfo() {
        return extraInfo;
    }

    public void setExtraInfo(Map<String, Object> extraInfo) {
        this.extraInfo = extraInfo;
    }

    @Override
    public String toString() {
        return "TtsResponse{" +
                "audioDataLength=" + (audioData != null ? audioData.length() : 0) +
                ", subtitleUrl='" + subtitleUrl + '\'' +
                ", audioFormat='" + audioFormat + '\'' +
                ", audioLength=" + audioLength +
                ", audioSampleRate=" + audioSampleRate +
                ", audioSize=" + audioSize +
                ", bitrate=" + bitrate +
                ", audioChannel=" + audioChannel +
                ", traceId='" + traceId + '\'' +
                '}';
    }
}
