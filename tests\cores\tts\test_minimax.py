from app.cores.tts import tts_factory

def test_minimax_tts():
    """测试Minimax TTS功能"""
    # 创建TTS实例
    tts = tts_factory.create('minimax')
    
    # 获取音色列表
    voices = tts.get_voices()
    print("\n=== 可用音色列表 ===")
    for voice in voices:
        print(f"ID: {voice['voice_id']}")
        print(f"名称: {voice['name']}")
        print(f"类型: {voice['type']}")
        print("描述:", ", ".join(voice['description']))
        print("---")
    
    # 如果有可用音色，测试转换
    if voices:
        voice_id = voices[0]['voice_id']
        print(f"\n=== 测试语音转换 (音色ID: {voice_id}) ===")
        
        # 基本转换
        result = tts.text_to_speech(
            text="你好，这是一个测试。",
            voice_id=voice_id
        )
        print("基本转换完成")
        
        # 带参数的转换
        result = tts.text_to_speech(
            text="让我们测试一下不同的参数。",
            voice_id=voice_id,
            options={
                'speed': 1.2,
                'vol': 1.5,
                'pitch': 1,
                'model': 'v2'
            }
        )
        print("参数转换完成")

if __name__ == '__main__':
    test_minimax_tts()
