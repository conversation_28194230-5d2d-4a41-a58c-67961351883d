from sqlalchemy import String
from sqlalchemy.orm import Mapped, mapped_column
from app.models.base import BaseModel

class User(BaseModel):
    """用户模型"""
    
    __tablename__ = 'users'

    username: Mapped[str] = mapped_column(
        String(50), 
        unique=True, 
        nullable=False, 
        comment='用户名'
    )
    email: Mapped[str] = mapped_column(
        String(120), 
        unique=True, 
        nullable=False, 
        comment='邮箱'
    )
    password: Mapped[str] = mapped_column(
        String(128), 
        nullable=False, 
        comment='密码'
    )
    nickname: Mapped[str] = mapped_column(
        String(50), 
        nullable=True, 
        comment='昵称'
    )
