package com.aimarket.config;

import com.aimarket.middleware.RequestIdInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web配置类
 * 
 * <AUTHOR> Market Team
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {

    @Autowired
    private AiMarketProperties aiMarketProperties;

    @Autowired
    private RequestIdInterceptor requestIdInterceptor;

    /**
     * 配置CORS跨域
     */
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        AiMarketProperties.Security security = aiMarketProperties.getSecurity();
        
        registry.addMapping("/**")
                .allowedOriginPatterns(security.getAllowedOrigins().split(","))
                .allowedMethods(security.getAllowedMethods().split(","))
                .allowedHeaders(security.getAllowedHeaders().split(","))
                .allowCredentials(true)
                .maxAge(3600);
    }

    /**
     * 配置拦截器
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 添加请求ID拦截器
        registry.addInterceptor(requestIdInterceptor)
                .addPathPatterns("/**");
    }
}
