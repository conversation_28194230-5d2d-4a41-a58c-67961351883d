import os
import json
import re

class EnvConfig:
    def __init__(self, config_dict=None):
        self._config = config_dict or {}
        for key, value in self._config.items():
            self._set_typed_attribute(key, value)

    def _set_typed_attribute(self, key, value):
        if isinstance(value, str):
            # 布尔值处理
            if value.lower() in ('true', 'false'):
                setattr(self, key, value.lower() == 'true')
            # 整数处理
            elif value.isdigit():
                setattr(self, key, int(value))
            # 浮点数处理
            elif self._is_float(value):
                setattr(self, key, float(value))
            # JSON对象处理
            elif (value.startswith('[') and value.endswith(']')) or \
                    (value.startswith('{') and value.endswith('}')):
                try:
                    setattr(self, key, json.loads(value))
                except json.JSONDecodeError:
                    setattr(self, key, value)
            # 逗号分隔列表处理
            elif ',' in value and not self._is_likely_sentence(value):
                items = [item.strip() for item in value.split(',')]
                # 尝试转换列表项的类型
                typed_items = []
                for item in items:
                    if item.isdigit():
                        typed_items.append(int(item))
                    elif self._is_float(item):
                        typed_items.append(float(item))
                    elif item.lower() in ('true', 'false'):
                        typed_items.append(item.lower() == 'true')
                    else:
                        typed_items.append(item)
                setattr(self, key, typed_items)
            # 空值处理
            elif value == '':
                setattr(self, key, None)
            # 其他字符串
            else:
                setattr(self, key, value)
        else:
            setattr(self, key, value)

    @staticmethod
    def _is_float(value):
        try:
            float(value)
            return True
        except ValueError:
            return False

    @staticmethod
    def _is_likely_sentence(value):
        """检查字符串是否可能是句子而非列表"""
        # 如果包含常见句子标点或超过一定数量的空格，可能是句子
        if re.search(r'[.!?;:]', value) or value.count(' ') > 3:
            return True
        return False

    def update_from_file(self, file_path):
        """从指定文件更新配置"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if not line or line.startswith('#'):
                        continue

                    if '=' in line:
                        key, value = line.split('=', 1)
                        self._config[key] = value
                        self._set_typed_attribute(key, value)
            print(f"成功从 {file_path} 加载配置")
            return True
        except FileNotFoundError:
            print(f"警告: 配置文件 {file_path} 未找到")
            return False

    @classmethod
    def from_env_files(cls, *file_paths, base_dir=None):
        """从多个.env文件加载配置"""
        if base_dir is None:
            # 获取调用者脚本所在目录
            base_dir = os.path.dirname(os.path.abspath(__file__))

        config = cls()

        for path in file_paths:
            full_path = os.path.join(base_dir, path) if not os.path.isabs(path) else path
            config.update_from_file(full_path)

        return config

    @classmethod
    def from_dotenv(cls, file_path=None):
        """兼容旧版API"""
        if file_path is None:
            base_dir = os.path.dirname(os.path.abspath(__file__))
            file_path = os.path.join(base_dir, '.env')

        return cls.from_env_files(file_path)



# 使用示例
if __name__ == "__main__":
    # 从当前目录的.env文件加载配置
    base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    ENV_CONFIG = EnvConfig.from_env_files('.env', base_dir=base_dir)
    print(ENV_CONFIG.MYSQL_HOST)
