import os
import time
import hashlib
import traceback
from typing import Dict, Any, Union, Optional
import requests
from app.cores import fetcher
from .schemas import (
    TTSRequest,
    ErrorResponse,
    SymbolSilSetting
)
from .exceptions import MoyinTTSException, ERROR_MAPPING

class MoyinTTS:
    """Moyin TTS API 客户端"""
    
    BASE_URL = "https://open.mobvoi.com/api/tts/v1"
    
    def __init__(self, appkey: str, secret: str):
        """
        初始化 Moyin TTS 客户端
        
        Args:
            appkey: API密钥
            secret: API密钥对应的secret
        """
        self.appkey = appkey
        self.secret = secret
        self.headers = {
            "Content-Type": "application/json"
        }
    
    def _generate_signature(self) -> tuple:
        """
        生成签名和时间戳
        
        Returns:
            tuple: (signature, timestamp)
            - signature: MD5签名
            - timestamp: 时间戳
        """
        timestamp = str(int(time.time()))
        message = '+'.join([self.appkey, self.secret, timestamp])
        
        m = hashlib.md5()
        m.update(message.encode("utf8"))
        signature = m.hexdigest()
        
        return signature, timestamp
    
    def _handle_error(self, response_data: Dict[str, Any]) -> None:
        """
        处理API错误响应
        
        Args:
            response_data: API响应数据
            
        Raises:
            MoyinTTSException: 当API返回错误时抛出
        """
        if response_data.get("status") == "error":
            error_message = response_data.get("errorMessage", {})
            code = error_message.get("code")
            desc = error_message.get("desc", "未知错误")
            
            if code:
                error_info = ERROR_MAPPING.get(code, ("APIError", "未知API错误"))
                error_class = error_info[0]
                error_msg = desc or error_info[1]
                
                raise MoyinTTSException(
                    message=f"{error_class}: {error_msg}",
                    status_code=code
                )
    
    def text_to_speech(
        self,
        request: Union[TTSRequest, Dict[str, Any]],
        symbol_sil: Optional[SymbolSilSetting] = None
    ) -> Union[bytes, tuple]:
        """
        将文本转换为语音
        
        Args:
            request: TTS请求参数，可以是TTSRequest对象或字典
            symbol_sil: 停顿符号设置，如果提供则会覆盖request中的symbol_sil
            
        Returns:
            Union[bytes, tuple]: 
            - 如果gen_srt为False，返回音频数据
            - 如果gen_srt为True，返回(音频数据, 字幕URL)元组
            
        Raises:
            MoyinTTSException: API调用出错时抛出
        """
        if isinstance(request, dict):
            request = TTSRequest(**request)
        
        # 生成签名和时间戳
        signature, timestamp = self._generate_signature()
        
        # 构建请求数据
        data = request.model_dump(exclude_none=True)
        
        # 如果提供了symbol_sil设置，转换为字符串格式
        if symbol_sil:
            symbol_sil_str = symbol_sil.to_string()
            if symbol_sil_str:
                data["symbol_sil"] = symbol_sil_str
        
        # 添加认证信息
        data.update({
            "appkey": self.appkey,
            "signature": signature,
            "timestamp": timestamp
        })
        
        try:
            response = fetcher.post(
                self.BASE_URL,
                headers=self.headers,
                json=data
            )


            # 检查Content-Type是否为音频格式
            content_type = response.headers.get("Content-Type", "")
            if "audio" in content_type:
                # 如果需要返回字幕URL
                if request.gen_srt:
                    srt_url = response.headers.get("srt_address")
                    return response.content.hex(), srt_url
                return response.content.hex()
            else:
                # 解析错误响应
                try:
                    response_data = response.json()
                    self._handle_error(response_data)
                except ValueError:
                    raise MoyinTTSException(
                        message=f"无效的响应格式: {response.text}"
                    )
                
        except requests.exceptions.HTTPError as e:
            print(traceback.format_exc())
            raise MoyinTTSException(
                message=f"HTTP错误: {str(e)}",
                status_code=e.response.status_code if hasattr(e, 'response') else None
            )
        except requests.exceptions.RequestException as e:
            print(traceback.format_exc())
            raise MoyinTTSException(
                message=f"请求错误: {str(e)}"
            )
        except Exception as e:
            print(traceback.format_exc())
            raise MoyinTTSException(
                message=f"未知错误: {str(e)}"
            )
