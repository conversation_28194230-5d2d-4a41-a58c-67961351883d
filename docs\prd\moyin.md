语音合成（TTS）
1. 接口描述
接口请求域名：https://open.mobvoi.com/api/tts/v1

接口请求频率限制：5次/秒

北京小问语音合成技术（TTS）可以将任意文本转化为语音，实现让机器和应用张口说话。 北京小问 TTS 技术可以应用到很多场景，例如，在视频APP中作为配音解说；小说 App 完成有声阅读，移动App语音播报新闻；智能设备语音提醒；车载导航语音合成的个性化语音播报等。

北京小问TTS服务提供了普通话、台湾腔、粤语、四川话、东北话等多种方言，数百个发音人，上千种风格，满足客户在不同场景的选择需求。 实时合成支持 SSML，语法详见 SSML 标记语言。

2. 请求参数
HTTP Method: 支持POST请求

调用参数及说明：

字段名	必填	类型	描述
text	是	String	要合成的文本内容，限制为1000字符。支持ssml标记语言，使用说明见附录3。
appkey	是	String	开发者在AI开放平台上申请的appkey。
signature	是	String	签名，通过“appkey+secret+timestamp”进行md5加密，得到的32位MD5值。其中加号也参与MD5的计算。
每次请求实时计算签名，签名有效期为10分钟。
timestamp	是	Long	当前时间戳，单位为秒。
speaker	否	String	合成音频指定发音人
默认值：galaxy_fastv2_moqingyan
其他发音人传值及计费价格请参考声音商店。pro发音人合成时长相对较长，慎重选用
audio_type	否	String	合成音频的格式
默认值：mp3
可选值：pcm/mp3/speex-wb-10/wav
只支持这四种格式中的一种
speed	否	Float	发音人合成的语速，支持小数点后两位
默认值：1.0
可选值：0.5-2.0
convert	否	String	默认值：无
可选值：robot
是否转化为机器声
rate	否	Long	音频采样率
默认值：无，由speaker指定默认值
可选值：8000/16000/24000
volume	否	Float	合成音量
默认值：1.0
可选值：0.1-1.0
pitch	否	Float	语调参数，参数小于0则语调变低，反之则高
默认值：0
可选值：-10<pitch<10
(streaming接口不支持)
symbol_sil	否	String	符号停顿时长映射方法（逗号分割)见下方停顿符号映射表
充值后自动开通权限
(streaming接口不支持)
如：symbol_sil=semi_200,exclamation_200,question_200,comma_200,stop_200
ignore_limit	否	Boolean	默认值：false
可选值：false/true
是否限制字符数，如果设置true，传输的文本可以超过1000字符限制，最大字符数3000
充值后自动开通权限
gen_srt	否	Boolean	可以控制是否生成对应的srt字幕文件。当ignore_limit为true时，audio_type为wav可以返回字幕，其他类型不行。默认不生成字幕文件。生成字幕文件需要额外付费，价格详情参考报价页。srt文件地址通过response header返回，参考下面response header样例。
默认值：false
可选值：false/true
merge_symbol	否	Boolean	粗粒度合成参数，默认为false。可以指定为true，打开后语气停顿会更加接近真人效果，merge_symbol开启会导致symbol_sil参数无效。
默认值：false
可选值：false/true
srt_len	否	Long	生成字幕的最大长度，如若为中文字幕遇到，,。!！？?;；等符号会自动分句拆分字幕
streaming	否	Boolean	是否流式输出，默认为false。可以指定为true，打开后ignore_limit 为true且audio_type 不为wav时，接口流式输出
Request Header设置

{
    "Content-Type": "application/json"
}
Request Body格式

body采用json格式传输

{
    "signature": "appkey+secret+timestamp的MD5值",
    "timestamp": "1665717322",
    "appkey": "开发者应用appkey",
    "speaker": "cissy_meet",
    "ignore_limit": true,
    "gen_srt": true,
    "audio_type": "mp3",
    "text": "海南长臂猿的叫声，高亢洪亮，响彻山谷。海南热带雨林国家公园是这种濒危灵长类动物的全球唯一栖息地。经过近年来的科学保护和生态恢复，海南长臂猿已由最少时的寥寥几只，恢复到5群35只，创造了世界珍稀动物保护的奇迹。国家公>园堪称最美国土，具有典型独特的自然生态系统、世界瞩目的野生动植物种。在海南热带雨林国家公园，这里生长着846种特有植物、145种国家重点保护野生动物，生物多样性指数与巴西亚马孙雨林相当。2018年4月，习近平总书记在庆祝海>南建省办经济特区30周年大会上强调，要积极开展国家公园体制试点，建设热带雨林等国家公园。2019年1月，总书记又主持召开中央全面深化改革委员会第六次会议，审议通过《海南热带雨林国家公园体制试点方案》。被称为海南“生态绿心”的这片最美国土迈出保护和建设的历史性一步。我国的国家公园在自然保护地体系中保护等级最高、生态价值最大、管控措施最严。",
    "speed": "1.0"
}
3. 返回值说明
如果成功合成，则直接返回语音流。

Response Header格式

{
    "Content-Type": "audio/mpeg",
    "srt_address": "https://mobvoi-speech-public.cn-bj.ufileos.com/mobvoi-tts/openapi/subtitle/d5c27272b28f8596ac0bf6183929d6de.srt?v=1666856127099"
}
body中是合成后的音频数据，可以保存成音频文件，或者交给播放器进行语音播放。

如果合成音频文件失败，返回json格式信息，包含具体的错误原因。

Error Response Body

{
  "status": "error",
  "errorMessage": {
    "code": 31002,
    "desc": "failed to convert text to speech"
  }
}
4. 符号停顿映射表
符号	设置方式
（名称+时长/ms)
时长为10的倍数，且大于等于0。
；;	semi_200
！!	exclamation_200
?？	question_200
，,	comma_200
。	stop_200
、	pause_200
5. 接口异常码
code	message	说明
400	根据不同的情况返回不同的信息，比如Bad Request, please check your parameters.（text empty）	必需参数缺失、为空或为 null。 或者，传递给必需参数或可选参数的值无效。 常见问题是text为空等。
401	根据不同的情况返回不同的信息，比如：Unauthorized. The current token is not authorized.	未授权，请求未经授权。 比如需要校验的请求。
404	param info not found!	参数缺失
408	Request Failed, please try again later.	合成失败，当前资源等问题引起的合成错误或者超时。建议重试几次。
413	Length Required, text should not be longer than 500.	文本超过500字符
500	check service error!	服务异常
502	Bad Request.	网络或服务器端问题。 也可能表示标头无效。
503	Service Unavailable, too many requests, please try again later.	请求过多已经超过了订阅允许的配额或请求速率。
10006	check is concurrent error!	appkey异常
10007	The parameter is abnormal!	参数异常
10008	The verification failed!	tts鉴权失败
10009	Both signature and referer are null, make sure request parameters contains signature or make sure header contains referer	signature异常
10010	appkey is null, make sure request parameters contains appkey	appkey有问题
10011	Necessary parameters are missing	缺少必需参数
10012	The app doesn't exist	应用不存在
10013	The service is disabled	服务已禁用
10014	The certification has expired	认证已过期
10015	Account freezing	账户冻结
10016	Unauthenticated request	未被认证的请求
10017	The referer authentication is not set	未设置该referer认证
10018	The clone task was not completed	clone任务未完成
10019	Unauthorized personal custom voice talents	未授权个人定制发音人
10020	The call name is offline	调用名已下线
10021	The user does not exist	用户不存在
10022	The number of calls per day for unauthenticated users is limited to 2000	未认证用户每天最多只能调用2000次
10023	Not in the IP address whitelist	不在ip白名单
10024	Service The number of services performed per second exceeded the upper limit	服务每秒服务次数超过限制
10025	Initialization failed	初始化失败
10026	The account balance is insufficient	用户账户余额不足
31000	invalid text	文本异常
31001	the max byte size of text is	文本超长
31002	e1.getMessage()	重试3次如果还有异常
31003	failed to convert text to speech	系统异常
31004	invalid net_type value	net_type参数校验有问题
31005	invalid speaker auth	用户自己的声音模型判断不合法
31006	invalid audio_type	audio_type异常
31007	invalid rate	rate参数异常
31008	invalid speed	speed参数异常
31009	invalid pitch	pitch参数异常
31010	invalid symbol_sil	symbol_sil参数异常
40080	Request Failed, Speaker do not exist!	发音人不存在
40081	Request Failed, Language do not match!	发音人支持语言和文本语言不一致
40084	The request contains sensitive information, Please replace the text and try re-synthesis!	请求带敏感词
部分异常说明
认证已过期：时间戳不对，和开放平台的认证是否审批通过没有关系

~~~
# coding=utf-8
import time
import hashlib
import os
import json
import requests

timestamp = str(int(time.time()))
appkey = '你的appkey'
secret = '你的secret'

message = '+'.join([appkey, secret, timestamp])

m = hashlib.md5()
m.update(message.encode("utf8"))
signature = m.hexdigest()

http_url = 'https://open.mobvoi.com/api/tts/v1'

def sample():
    data = {
        'text': '出门问问成立于2012年，是一家以语音交互和软硬结合为核心的人工智能公司，为全球40多个国家和地区的消费者、企业提供人工智能产品和服务。',
        'speaker': 'xiaoyi_meet',
        'audio_type': 'mp3',
        'speed': 1.0,
        #'symbol_sil': 'semi_250,exclamation_300,question_250,comma_200,stop_300,pause_150,colon_200', # 停顿调节需要对appkey授权后才可以使用，授权前传参无效。
        #'ignore_limit': True, # 忽略1000字符长度限制，需要对appkey授权后才可以使用
        'gen_srt': False, # 是否生成srt字幕文件，默认不开启。如果开启生成字幕，需要额外计费。生成好的srt文件地址将通过response header中的srt_address字段返回。
        'appkey': appkey,
        'timestamp': timestamp,
        'signature': signature
    }
    try:
        headers = {'Content-Type': 'application/json'}
        response = requests.post(url=http_url, headers=headers, data=json.dumps(data))
        content = response.content

        with open(os.path.join(os.path.dirname(os.path.abspath("__file__")), "sample.mp3"), "wb") as f:
            f.write(content)
    except Exception as e:
        print("error: {0}".format(e))

def sampleWithSrt():
    data = {
        'text': '出门问问成立于2012年，是一家以语音交互和软硬结合为核心的人工智能公司，为全球40多个国家和地区的消费者、企业提供人工智能产品和服务。',
        'speaker': 'xiaoyi_meet',
        'audio_type': 'wav',
        'speed': 1.0,
        #'symbol_sil': 'semi_250,exclamation_300,question_250,comma_200,stop_300,pause_150,colon_200', # 停顿调节需要对appkey授权后才可以使用，授权前传参无效。
        #'ignore_limit': True, # 忽略1000字符长度限制，需要对appkey授权后才可以使用
        'gen_srt': True, # 是否生成srt字幕文件，默认不开启。如果开启生成字幕，需要额外计费。生成好的srt文件地址将通过response header中的srt_address字段返回。
        'appkey': appkey,
        'timestamp': timestamp,
        'signature': signature
    }
    try:
        headers = {'Content-Type': 'application/json'}
        print(json.dumps(data))
        response = requests.post(url=http_url, headers=headers, data=json.dumps(data))
        content = response.content

        with open(os.path.join(os.path.dirname(os.path.abspath("__file__")), "sample.mp3"), "wb") as f:
            f.write(content)

        srtUrl = response.headers.get('srt_address', None)
        if srtUrl is None:
            print('not found srt url from response header')
            return

        print('srt url:', srtUrl)
        content = requests.get(url=srtUrl).content
        with open(os.path.join(os.path.dirname(os.path.abspath("__file__")), "sample.srt"), "wb") as f:
            f.write(content)

    except Exception as e:
        print("error: {0}".format(e))
~~~