"""
封装请求函数，增加重试和异常处理
"""
import time
import os
import requests


FETCHER_TIMEOUT = int(os.getenv('FETCHER_TIMEOUT'))
FETCHER_RETRY_TIMES = int(os.getenv('FETCHER_RETRY_TIMES'))
FETCHER_RETRY_WAIT_TIME = int(os.getenv('FETCHER_RETRY_WAIT_TIME'))

def get(url, params=None, force_ok=True, ok_status_list=None, **kwargs):
    """
    20221116 增加 force_ok True 配置下，支持传递特定状态码 作为 response.ok status_code 的补充，
    用于解决特定场景下支持指定 status_code 为正常结果
    """
    e_list = []
    for _ in range(FETCHER_RETRY_TIMES):
        try:
            timeout = kwargs.get('timeout', FETCHER_TIMEOUT)
            response = requests.get(url, params=params, timeout=timeout, **kwargs)
            # 20220824 验证请求是否正常响应
            if force_ok:
                if response.ok:
                    return response
                elif ok_status_list and response.status_code in ok_status_list:
                    return response
                else:
                    print(f'请求异常：url: {url} params：{params} 响应code:{response.status_code},响应文本：{response.text}')
                    time.sleep(FETCHER_RETRY_WAIT_TIME)
                    e_list.append(Exception(f'url: {url} params：{params} '
                                            f'响应code:{response.status_code},响应文本：{response.text}'))
            else:
                return response
        except requests.exceptions.ConnectionError as e:
            print(f"url: {url}", e, "wait 30 secs.")
            e_list.append(e)
            time.sleep(30)
        except Exception as e:
            print(f"url: {url}", e)
            e_list.append(e)
            time.sleep(FETCHER_RETRY_WAIT_TIME * 2)
    else:
        raise e_list[-1]


def post(url, data=None, json=None, force_ok=True, ok_status_list=None, **kwargs):
    e_list = []
    for _ in range(FETCHER_RETRY_TIMES):
        try:
            timeout = kwargs.get('timeout', FETCHER_TIMEOUT)
            response = requests.post(url, data=data, json=json, timeout=timeout, **kwargs)
            # print(response.text)
            # 20220824 验证请求是否正常响应
            if force_ok:
                if response.ok:
                    return response
                elif ok_status_list and response.status_code in ok_status_list:
                    return response
                else:
                    print(f'请求异常：url: {url} 响应code:{response.status_code},响应文本：{response.text}')
                    time.sleep(FETCHER_RETRY_WAIT_TIME)
                    e_list.append(Exception(f'url: {url} payload：{data or data} '
                                            f'响应code:{response.status_code},响应文本：{response.text}'))
            else:
                return response
        except requests.exceptions.ConnectionError as e:
            print(f"url: {url}", e, "wait 30 secs.")
            e_list.append(e)
            time.sleep(30)
        except Exception as e:
            e_list.append(e)
            time.sleep(FETCHER_RETRY_WAIT_TIME * 2)
    else:
        raise e_list[-1]


def put(url, data=None, json=None, force_ok=True, ok_status_list=None, **kwargs):
    e_list = []
    for _ in range(FETCHER_RETRY_TIMES):
        try:
            timeout = kwargs.get('timeout', FETCHER_TIMEOUT)
            response = requests.put(url, data=data, json=json, timeout=timeout, **kwargs)
            # 20220824 验证请求是否正常响应
            if force_ok:
                if response.ok:
                    return response
                elif ok_status_list and response.status_code in ok_status_list:
                    return response
                else:
                    print(f'请求异常：url: {url} 响应code:{response.status_code},响应文本：{response.text}')
                    time.sleep(FETCHER_RETRY_WAIT_TIME)
                    e_list.append(Exception(f'响应code:{response.status_code},响应文本：{response.text}'))
            else:
                return response
        except requests.exceptions.ConnectionError as e:
            print(f"url: {url}", e, "wait 30 secs.")
            e_list.append(e)
            time.sleep(30)
        except Exception as e:
            e_list.append(e)
            time.sleep(FETCHER_RETRY_WAIT_TIME * 2)
    else:
        raise e_list[-1]
