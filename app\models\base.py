from datetime import datetime
from typing import Any, Dict, Optional
from sqlalchemy import Integer, DateTime, Boolean
from sqlalchemy.orm import Mapped, mapped_column, DeclarativeBase
from app.extensions import db

class Base(DeclarativeBase):
    """SQLAlchemy 基类"""
    pass

class BaseModel(Base):
    """模型基类"""
    
    __abstract__ = True

    id: Mapped[int] = mapped_column(
        Integer, 
        primary_key=True, 
        autoincrement=True, 
        comment='主键ID'
    )
    create_time: Mapped[datetime] = mapped_column(
        DateTime, 
        nullable=False, 
        default=datetime.utcnow, 
        comment='创建时间'
    )
    update_time: Mapped[datetime] = mapped_column(
        DateTime, 
        nullable=False, 
        default=datetime.utcnow, 
        onupdate=datetime.utcnow, 
        comment='更新时间'
    )

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            column.name: getattr(self, column.name)
            for column in self.__table__.columns
        }

    def save(self) -> 'BaseModel':
        """保存到数据库"""
        db.session.add(self)
        db.session.commit()
        return self

    def delete(self, hard: bool = False) -> None:
        """删除
        Args:
            hard: 是否硬删除
        """
        if hard:
            db.session.delete(self)
        else:
            self.is_deleted = True
        db.session.commit()

    @classmethod
    def get_by_id(cls, id_: int) -> Optional['BaseModel']:
        """根据ID获取记录"""
        return db.session.get(cls, id_)

    @classmethod
    def get_all(
        cls,
        page: int = 1,
        per_page: int = 10,
        only_active: bool = True
    ) -> Any:
        """获取所有记录
        Args:
            page: 页码
            per_page: 每页数量
            only_active: 是否只返回未删除的记录
        """
        query = db.select(cls)
        if only_active:
            query = query.filter_by(is_deleted=False)
        
        return db.paginate(
            query,
            page=page,
            per_page=per_page
        )
