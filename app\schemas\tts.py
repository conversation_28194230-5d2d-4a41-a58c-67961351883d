from marshmallow import Schema, fields, validate

class TTSRequestSchema(Schema):
    """TTS请求参数schema"""
    text = fields.String(required=True, validate=validate.Length(min=1, max=5000))
    voice_id = fields.String(required=True)
    style = fields.String(missing=None)
    speed = fields.Float(missing=1.0, validate=validate.Range(min=0.5, max=2.0))
    volume = fields.Float(missing=1.0, validate=validate.Range(min=0, max=10.0))
    pitch = fields.Float(missing=0, validate=validate.Range(min=-12, max=12))
    audio_format = fields.String(missing="mp3", validate=validate.OneOf(["mp3", "wav"]))
    streaming = fields.Boolean(missing=False)
    subtitle = fields.Boolean(missing=False)
    merge_symbol = fields.Boolean(missing=True)
    output_format = fields.String(missing="hex", validate=validate.OneOf(["hex", "url"]))

class TTSResponseSchema(Schema):
    """TTS响应schema"""
    audio_data = fields.String(required=True)
    subtitle_url = fields.String(allow_none=True)
    duration = fields.Float()
    format = fields.String()
