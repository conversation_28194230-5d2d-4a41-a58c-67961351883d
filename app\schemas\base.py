from marshmallow import Schema, fields, EXCLUDE

class BaseSchema(Schema):
    """Schema基类"""
    
    class Meta:
        # 未知字段排除
        unknown = EXCLUDE
        # 排序输出
        ordered = True

    # 通用字段
    id = fields.Integer(dump_only=True)
    created_at = fields.DateTime(dump_only=True)
    updated_at = fields.DateTime(dump_only=True)
    is_deleted = fields.Boolean(dump_only=True)

class PaginationSchema(Schema):
    """分页Schema"""
    
    class Meta:
        unknown = EXCLUDE
        ordered = True

    total = fields.Integer()
    page = fields.Integer()
    per_page = fields.Integer()
    pages = fields.Integer()
    items = fields.List(fields.Dict())

class QuerySchema(Schema):
    """查询参数Schema"""
    
    class Meta:
        unknown = EXCLUDE

    page = fields.Integer(missing=1, validate=lambda x: x > 0)
    per_page = fields.Integer(missing=10, validate=lambda x: 0 < x <= 100)
    only_active = fields.Bo<PERSON>an(missing=True)
