package com.aimarket.core.tts;

import java.util.List;
import java.util.Map;

/**
 * TTS提供商配置信息
 * 
 * <AUTHOR> Market Team
 */
public class TtsProviderConfig {

    private String providerName;
    private List<String> apiKeys;
    private String baseUrl;
    private int timeout;
    private int retryCount;
    private Map<String, Object> additionalConfig;

    // Constructors
    public TtsProviderConfig() {
    }

    public TtsProviderConfig(String providerName, List<String> apiKeys, String baseUrl) {
        this.providerName = providerName;
        this.apiKeys = apiKeys;
        this.baseUrl = baseUrl;
    }

    // Getters and Setters
    public String getProviderName() {
        return providerName;
    }

    public void setProviderName(String providerName) {
        this.providerName = providerName;
    }

    public List<String> getApiKeys() {
        return apiKeys;
    }

    public void setApiKeys(List<String> apiKeys) {
        this.apiKeys = apiKeys;
    }

    public String getBaseUrl() {
        return baseUrl;
    }

    public void setBaseUrl(String baseUrl) {
        this.baseUrl = baseUrl;
    }

    public int getTimeout() {
        return timeout;
    }

    public void setTimeout(int timeout) {
        this.timeout = timeout;
    }

    public int getRetryCount() {
        return retryCount;
    }

    public void setRetryCount(int retryCount) {
        this.retryCount = retryCount;
    }

    public Map<String, Object> getAdditionalConfig() {
        return additionalConfig;
    }

    public void setAdditionalConfig(Map<String, Object> additionalConfig) {
        this.additionalConfig = additionalConfig;
    }

    /**
     * 获取随机API密钥
     */
    public String getRandomApiKey() {
        if (apiKeys == null || apiKeys.isEmpty()) {
            return null;
        }
        int index = (int) (Math.random() * apiKeys.size());
        return apiKeys.get(index);
    }

    /**
     * 检查配置是否有效
     */
    public boolean isValid() {
        return providerName != null && !providerName.isEmpty() &&
               apiKeys != null && !apiKeys.isEmpty() &&
               baseUrl != null && !baseUrl.isEmpty();
    }

    @Override
    public String toString() {
        return "TtsProviderConfig{" +
                "providerName='" + providerName + '\'' +
                ", apiKeysCount=" + (apiKeys != null ? apiKeys.size() : 0) +
                ", baseUrl='" + baseUrl + '\'' +
                ", timeout=" + timeout +
                ", retryCount=" + retryCount +
                '}';
    }
}
