package com.aimarket.repository;

import com.aimarket.entity.Product;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

/**
 * 产品数据访问层
 * 
 * <AUTHOR> Market Team
 */
@Repository
public interface ProductRepository extends JpaRepository<Product, Long> {

    /**
     * 根据状态查找产品
     */
    Page<Product> findByStatus(String status, Pageable pageable);

    /**
     * 查找活跃产品
     */
    Page<Product> findByStatusOrderByCreatedAtDesc(String status, Pageable pageable);

    /**
     * 根据分类查找产品
     */
    Page<Product> findByCategory(String category, Pageable pageable);

    /**
     * 根据分类和状态查找产品
     */
    Page<Product> findByCategoryAndStatus(String category, String status, Pageable pageable);

    /**
     * 查找特色产品
     */
    List<Product> findByIsFeaturedTrueAndStatusOrderByCreatedAtDesc(String status);

    /**
     * 根据价格范围查找产品
     */
    Page<Product> findByPriceBetweenAndStatus(BigDecimal minPrice, BigDecimal maxPrice, String status, Pageable pageable);

    /**
     * 根据名称模糊查询产品
     */
    @Query("SELECT p FROM Product p WHERE p.name LIKE %:name% AND p.status = :status")
    Page<Product> findByNameLikeAndStatus(@Param("name") String name, @Param("status") String status, Pageable pageable);

    /**
     * 根据名称或描述模糊查询产品
     */
    @Query("SELECT p FROM Product p WHERE (p.name LIKE %:keyword% OR p.description LIKE %:keyword%) AND p.status = :status")
    Page<Product> findByNameOrDescriptionLikeAndStatus(@Param("keyword") String keyword, @Param("status") String status, Pageable pageable);

    /**
     * 根据标签查找产品
     */
    @Query("SELECT p FROM Product p WHERE p.tags LIKE %:tag% AND p.status = :status")
    Page<Product> findByTagsContainingAndStatus(@Param("tag") String tag, @Param("status") String status, Pageable pageable);

    /**
     * 查找库存不足的产品
     */
    @Query("SELECT p FROM Product p WHERE p.stock <= :threshold AND p.status = 'active'")
    List<Product> findLowStockProducts(@Param("threshold") Integer threshold);

    /**
     * 查找热销产品
     */
    @Query("SELECT p FROM Product p WHERE p.status = 'active' ORDER BY p.saleCount DESC")
    Page<Product> findTopSellingProducts(Pageable pageable);

    /**
     * 查找热门产品（按浏览量）
     */
    @Query("SELECT p FROM Product p WHERE p.status = 'active' ORDER BY p.viewCount DESC")
    Page<Product> findMostViewedProducts(Pageable pageable);

    /**
     * 统计指定分类的产品数量
     */
    long countByCategoryAndStatus(String category, String status);

    /**
     * 统计活跃产品数量
     */
    long countByStatus(String status);

    /**
     * 更新产品浏览量
     */
    @Modifying
    @Query("UPDATE Product p SET p.viewCount = p.viewCount + 1 WHERE p.id = :id")
    void incrementViewCount(@Param("id") Long id);

    /**
     * 更新产品销量
     */
    @Modifying
    @Query("UPDATE Product p SET p.saleCount = p.saleCount + :count WHERE p.id = :id")
    void incrementSaleCount(@Param("id") Long id, @Param("count") Long count);

    /**
     * 批量更新产品状态
     */
    @Modifying
    @Query("UPDATE Product p SET p.status = :status WHERE p.id IN :ids")
    void updateStatusByIds(@Param("ids") List<Long> ids, @Param("status") String status);
}
