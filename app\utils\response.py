from typing import Any, Dict, List, Optional, Union
from flask import jsonify

class APIResponse:
    """统一API响应格式"""
    
    @staticmethod
    def success(
        data: Optional[Union[Dict, List, str]] = None,
        message: str = "success",
        code: int = 200
    ):
        """成功响应"""
        return jsonify({
            "code": code,
            "bizcode": 1501010,
            "message": message,
            "data": data or {}
        })

    @staticmethod
    def error(
        message: str = "error",
        code: int = 40000,
        data: Optional[Any] = None
    ):
        """错误响应"""
        return jsonify({
            "code": code,
            "message": message,
            "data": data or {}
        }), code if 400 <= code <= 600 else 500

class ErrorCode:
    """错误码定义"""
    SUCCESS = 20000
    PARAMS_ERROR = 40000
    UNAUTHORIZED = 40100
    FORBIDDEN = 40300
    NOT_FOUND = 40400
    SERVER_ERROR = 50000
