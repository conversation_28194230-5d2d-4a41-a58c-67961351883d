# TTS 功能模块设计文档

## 概述

TTS（Text-to-Speech，文本转语音）模块是 AI Market 项目的核心功能模块，负责将文本转换为自然语音。该模块采用工厂模式和适配器模式设计，支持多个TTS服务提供商，具备高度的可扩展性和可维护性。

## 设计目标

- **多厂商支持**: 统一接口支持多个TTS服务提供商
- **高可用性**: 支持多API密钥轮询，提高服务可用性
- **易扩展性**: 新增TTS提供商只需实现适配器接口
- **统一体验**: 屏蔽不同厂商API差异，提供一致的调用体验
- **错误处理**: 完善的异常处理和错误码映射
- **配置管理**: 集中化的配置管理和环境变量支持

## 架构设计

### 整体架构

```
┌─────────────────────────────────────────────────────────────┐
│                    TTS Service Layer                        │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   Controllers   │  │    Services     │  │   Schemas    │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                    TTS Core Layer                           │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   TTS Factory   │  │  Config Manager │  │  Exceptions  │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                   Adapter Layer                             │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │ Minimax Adapter │  │  Moyin Adapter  │  │ Base Adapter │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                  Provider Layer                             │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │  Minimax Client │  │   Moyin Client  │  │ Other Client │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 核心组件

#### 1. TTS Factory（工厂类）
- **职责**: 管理和创建TTS提供商实例
- **特性**: 
  - 提供商注册机制
  - 配置管理集成
  - 多密钥轮询支持
- **位置**: `app/cores/tts/factory.py`

#### 2. Base TTS（基础抽象类）
- **职责**: 定义TTS服务的统一接口
- **方法**:
  - `text_to_speech()`: 文本转语音
  - `get_voices()`: 获取音色列表
- **位置**: `app/cores/tts/base.py`

#### 3. TTS Adapters（适配器）
- **职责**: 适配不同厂商的API接口
- **实现**: 
  - MinimaxTTSAdapter
  - MoyinTTSAdapter
- **位置**: `app/cores/tts/adapters/`

#### 4. Provider Clients（厂商客户端）
- **职责**: 具体厂商API的封装实现
- **位置**: `app/cores/tts/minimax/`, `app/cores/tts/moyin/`

## 目录结构

```
app/cores/tts/
├── __init__.py                 # 模块初始化和工厂实例
├── base.py                     # TTS基础抽象类
├── factory.py                  # TTS工厂类
├── config.py                   # 配置管理器
├── exceptions.py               # 异常定义
├── schemas.py                  # 统一数据模型
├── adapters/                   # 适配器目录
│   ├── __init__.py
│   ├── minimax.py             # Minimax适配器
│   └── moyin.py               # Moyin适配器
├── minimax/                    # Minimax厂商实现
│   ├── __init__.py
│   ├── tts.py                 # Minimax客户端
│   ├── schemas.py             # Minimax数据模型
│   └── exceptions.py          # Minimax异常
└── moyin/                      # Moyin厂商实现
    ├── __init__.py
    ├── tts.py                 # Moyin客户端
    ├── schemas.py             # Moyin数据模型
    └── exceptions.py          # Moyin异常
```

## 数据模型设计

### 统一数据模型

#### TTSOptions（TTS选项）
```python
class TTSOptions(BaseModel):
    # 基本参数
    style: Optional[str] = None          # 语气风格
    speed: float = 1.0                   # 语速 (0.5-2.0)
    volume: float = 1.0                  # 音量 (0-10.0)
    pitch: float = 0                     # 语调 (-12-12)
    
    # 音频设置
    audio_format: str = "mp3"            # 音频格式
    bitrate: Optional[int] = None        # 比特率
    channel: Optional[int] = 1           # 声道数
    
    # 特殊功能
    streaming: bool = False              # 是否流式输出
    subtitle: bool = False               # 是否生成字幕
    
    # 厂商特定参数
    provider_options: Dict[str, Any] = {}
```

#### TTSResult（TTS结果）
```python
class TTSResult(BaseModel):
    audio_data: str                      # 音频数据（hex编码）
    subtitle_url: Optional[str] = None   # 字幕URL
    extra_info: Dict[str, Any] = {}      # 额外信息
```

### 厂商特定数据模型

#### Minimax 数据模型
- **VoiceSetting**: 音色设置
- **AudioSetting**: 音频设置
- **TTSRequest**: 请求参数
- **TTSResponse**: 响应数据

#### Moyin 数据模型
- **TTSRequest**: 请求参数
- **SymbolSilSetting**: 停顿符号设置
- **AudioSetting**: 音频设置

## 工厂模式实现

### 注册机制

```python
@TTSFactory.register("minimax")
class MinimaxTTSAdapter(BaseTTS):
    def __init__(self, api_key: str, group_id: str):
        # 初始化逻辑
        pass
```

### 实例创建

```python
# 自动选择可用的API密钥
tts = tts_factory.create('minimax')

# 调用统一接口
result = tts.text_to_speech(
    text="要转换的文本",
    voice_id="voice_id",
    options=TTSOptions(speed=1.2)
)
```

## 配置管理

### 环境变量配置

```bash
# Minimax 配置
minimax_api_key=your_api_key
minimax_group_id=your_group_id

# Moyin 配置
moyin_app_key=your_app_key
moyin_secret=your_secret
```

### 配置管理器

```python
class TTSConfigManager:
    def __init__(self):
        self._providers = {
            "minimax": TTSProviderConfig(
                name="minimax",
                keys=[{
                    "api_key": os.getenv('minimax_api_key'),
                    "group_id": os.getenv('minimax_group_id')
                }]
            ),
            "moyin": TTSProviderConfig(
                name="moyin", 
                keys=[{
                    "appkey": os.getenv('moyin_app_key'),
                    "secret": os.getenv('moyin_secret')
                }]
            )
        }
```

## 异常处理

### 异常层次结构

```
TTSException (基础异常)
├── TTSProviderException (提供商异常)
└── TTSConfigException (配置异常)
```

### 错误码映射

每个厂商都有自己的错误码映射：

```python
# Minimax 错误码映射
ERROR_MAPPING = {
    1000: ("UnknownError", "未知错误"),
    1001: ("TimeoutError", "请求超时"),
    1002: ("RateLimitError", "触发限流"),
    1004: ("AuthenticationError", "鉴权失败"),
    # ...
}
```

## API 接口设计

### REST API 端点

#### 文本转语音
```
POST /audio/tts
Content-Type: application/json

{
    "text": "要转换的文本",
    "voice_id": "voice_001",
    "style": "happy",
    "speed": 1.2,
    "volume": 1.0,
    "pitch": 0,
    "audio_format": "mp3",
    "subtitle": false,
    "output_format": "hex"
}
```

#### 获取音色列表
```
GET /audio/voices
```

### 响应格式

```json
{
    "code": 200,
    "message": "success",
    "data": {
        "audio_data": "hex_encoded_audio_data",
        "subtitle_url": "http://example.com/subtitle.srt",
        "format": "mp3"
    }
}
```

## 服务流程

### TTS 调用流程

```
1. 客户端请求 → Controllers/audio/tts.py
2. 参数验证 → schemas/tts.py (Marshmallow)
3. 业务处理 → services/tts.py
4. 音色解析 → repositories/voice.py
5. 厂商选择 → cores/tts/factory.py
6. 参数适配 → cores/tts/adapters/
7. API 调用 → cores/tts/minimax/ 或 cores/tts/moyin/
8. 结果处理 → utils/audio.py
9. 响应返回 → 客户端
```

### 音色管理流程

```
1. 获取音色请求 → Controllers/audio/voices.py
2. 查询数据库 → repositories/voice.py
3. 按语言分组 → services/tts.py
4. 返回音色列表 → 客户端
```

## 扩展指南

### 添加新的TTS提供商

1. **创建厂商目录**
   ```
   app/cores/tts/new_provider/
   ├── __init__.py
   ├── tts.py
   ├── schemas.py
   └── exceptions.py
   ```

2. **实现客户端类**
   ```python
   class NewProviderTTS:
       def text_to_speech(self, request):
           # 实现具体的API调用逻辑
           pass
   ```

3. **创建适配器**
   ```python
   @TTSFactory.register("new_provider")
   class NewProviderTTSAdapter(BaseTTS):
       def text_to_speech(self, text, voice_id, options):
           # 适配统一接口
           pass
   ```

4. **添加配置**
   ```python
   # 在 config.py 中添加配置
   "new_provider": TTSProviderConfig(
       name="new_provider",
       keys=[{"api_key": os.getenv('new_provider_api_key')}]
   )
   ```

5. **注册适配器**
   ```python
   # 在 __init__.py 中导入适配器
   from .adapters import NewProviderTTSAdapter
   ```

## 性能优化

### 缓存策略
- 音色列表缓存
- 配置信息缓存
- 频繁请求结果缓存

### 连接池
- HTTP连接复用
- 请求超时设置
- 重试机制

### 负载均衡
- 多API密钥轮询
- 厂商故障转移
- 请求分发策略

## 监控与日志

### 关键指标
- TTS调用成功率
- 平均响应时间
- 各厂商使用情况
- 错误率统计

### 日志记录
- 请求参数记录
- 响应时间记录
- 错误详情记录
- 厂商切换记录

## 测试策略

### 单元测试
- 工厂类测试
- 适配器测试
- 配置管理测试
- 异常处理测试

### 集成测试
- 端到端TTS调用测试
- 多厂商切换测试
- 错误恢复测试

### 性能测试
- 并发请求测试
- 大文本转换测试
- 长时间运行测试

## 使用示例

### 基础使用

```python
from app.cores.tts import tts_factory
from app.cores.tts.schemas import TTSOptions

# 创建TTS实例（自动选择可用密钥）
tts = tts_factory.create('minimax')

# 基础文本转语音
result = tts.text_to_speech(
    text="你好，欢迎使用AI Market TTS服务！",
    voice_id="voice_001"
)

print(f"音频数据: {result.audio_data[:100]}...")  # 显示前100个字符
print(f"字幕URL: {result.subtitle_url}")
```

### 高级使用

```python
# 带参数的转换
options = TTSOptions(
    style="happy",           # 快乐语气
    speed=1.2,              # 1.2倍语速
    volume=1.5,             # 1.5倍音量
    pitch=2,                # 提高音调
    audio_format="wav",     # WAV格式
    subtitle=True,          # 生成字幕
    provider_options={      # 厂商特定参数
        "model": "v2",
        "emotion": "excited"
    }
)

result = tts.text_to_speech(
    text="这是一个带有特殊参数的语音转换示例。",
    voice_id="voice_002",
    options=options
)
```

### 服务层使用

```python
from app.services.tts import TTSService

# 通过服务层调用
tts_service = TTSService()

# 获取所有可用音色
voices = tts_service.get_all_voices()

# 文本转语音
result = tts_service.text_to_speech({
    "text": "服务层调用示例",
    "voice_id": "minimax_voice_001",
    "speed": 1.0,
    "volume": 1.0,
    "audio_format": "mp3",
    "output_format": "url"  # 返回OSS URL
})
```

## 厂商对比

### Minimax vs Moyin

| 特性 | Minimax | Moyin |
|------|---------|-------|
| **音色数量** | 丰富的系统音色 | 多样化音色库 |
| **语言支持** | 中英文 | 主要中文 |
| **音质** | 高质量 | 高质量 |
| **特殊功能** | 流式输出、字幕 | 字幕、停顿控制 |
| **定价** | 按字符计费 | 按字符计费 |
| **API稳定性** | 高 | 高 |

### 选择建议

- **Minimax**: 适合需要英文支持和流式输出的场景
- **Moyin**: 适合中文场景和需要精细停顿控制的应用

## 故障处理

### 常见问题及解决方案

#### 1. API密钥失效
```python
# 错误信息
TTSConfigException: No API keys configured for provider: minimax

# 解决方案
1. 检查环境变量配置
2. 验证API密钥有效性
3. 确认配置文件路径正确
```

#### 2. 厂商服务不可用
```python
# 错误信息
TTSProviderException: [minimax] Request timeout

# 解决方案
1. 自动切换到备用厂商
2. 实现重试机制
3. 监控厂商服务状态
```

#### 3. 音色ID不存在
```python
# 错误信息
ValidationError: Voice ID not found

# 解决方案
1. 验证音色ID有效性
2. 提供音色列表查询接口
3. 实现音色ID映射
```

### 降级策略

```python
class TTSService:
    def text_to_speech_with_fallback(self, params):
        providers = ['minimax', 'moyin']

        for provider in providers:
            try:
                # 尝试使用当前提供商
                return self._call_provider(provider, params)
            except TTSProviderException as e:
                # 记录错误，尝试下一个提供商
                logger.warning(f"Provider {provider} failed: {e}")
                continue

        # 所有提供商都失败
        raise TTSException("All providers failed")
```

## 安全考虑

### API密钥管理
- 环境变量存储
- 密钥轮换机制
- 访问权限控制

### 输入验证
- 文本长度限制
- 特殊字符过滤
- SQL注入防护

### 输出安全
- 音频文件扫描
- 内容审核
- 访问控制

## 部署配置

### 环境变量示例

```bash
# .env 文件
# Minimax 配置
minimax_api_key=sk-xxxxxxxxxxxxxxxx
minimax_group_id=xxxxxxxxxxxxxxxx
MINIMAX_BASE_URL=https://api.minimax.chat
MINIMAX_DEFAULT_MODEL=speech-01

# Moyin 配置
moyin_app_key=xxxxxxxxxxxxxxxx
moyin_secret=xxxxxxxxxxxxxxxx
MOYIN_BASE_URL=https://api.moyin.com

# OSS 配置（用于音频文件上传）
OSS_URL=https://oss.example.com/upload

# 缓存配置
ENABLE_REDIS_CACHE=true
REDIS_URL=redis://localhost:6379/0
```

### Docker 配置

```dockerfile
# Dockerfile 片段
ENV minimax_api_key=${MINIMAX_API_KEY}
ENV minimax_group_id=${MINIMAX_GROUP_ID}
ENV moyin_app_key=${MOYIN_APP_KEY}
ENV moyin_secret=${MOYIN_SECRET}
```

## 版本兼容性

### API版本管理
- 向后兼容保证
- 版本号标识
- 废弃功能通知

### 厂商API更新
- 定期检查API变更
- 适配器版本管理
- 平滑升级策略

---

*本文档详细描述了TTS模块的设计架构和实现细节，为开发和维护提供全面指导。*
