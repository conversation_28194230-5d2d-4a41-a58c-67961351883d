from typing import Optional, Any

class APIException(Exception):
    """API异常基类"""
    
    def __init__(
        self,
        message: str = "An error occurred",
        code: int = 40000,
        data: Optional[Any] = None
    ):
        self.message = message
        self.code = code
        self.data = data
        super().__init__(self.message)

class ValidationError(APIException):
    """参数验证错误"""
    
    def __init__(
        self,
        message: str = "Invalid parameters",
        data: Optional[Any] = None
    ):
        super().__init__(message=message, code=40000, data=data)

class AuthenticationError(APIException):
    """认证错误"""
    
    def __init__(
        self,
        message: str = "Authentication failed",
        data: Optional[Any] = None
    ):
        super().__init__(message=message, code=40100, data=data)

class AuthorizationError(APIException):
    """授权错误"""
    
    def __init__(
        self,
        message: str = "Permission denied",
        data: Optional[Any] = None
    ):
        super().__init__(message=message, code=40300, data=data)

class NotFoundError(APIException):
    """资源不存在错误"""
    
    def __init__(
        self,
        message: str = "Resource not found",
        data: Optional[Any] = None
    ):
        super().__init__(message=message, code=40400, data=data)

class BusinessError(APIException):
    """业务逻辑错误"""
    
    def __init__(
        self,
        message: str = "Business logic error",
        code: int = 40000,
        data: Optional[Any] = None
    ):
        super().__init__(message=message, code=code, data=data)
