from app.cores.tts import tts_factory
from app.cores.tts.schemas import TTSOptions

# 创建TTS实例（会自动使用随机密钥）
# tts = tts_factory.create('moyin')
#
# # 转换文本为语音
# result = tts.text_to_speech(
#     text="要转换的文本",
#     voice_id="giikins_Belma",
#     options=TTSOptions(speed=1,
#                        style='happy'
#                        # subtitle=True,
#                        # provider_options={"emotion": "happy"}
#                        )
# )
# print(type(result))
# print(result.audio_data)
# print(result.subtitle_url)
# print(result.extra_info)
# print('-'*50)

# # 创建TTS实例（会自动使用随机密钥）
tts = tts_factory.create('minimax')

# 转换文本为语音
# result = tts.text_to_speech(
#     text="要转换的文本",
#     voice_id="giikin_Alice",
#     options=TTSOptions(speed=1,
#                        # subtitle=True,
#                        # provider_options={"emotion": "happy"}
#                        )
# )
# print(type(result))
# print(result.audio_data)
# print(result.subtitle_url)


# 获取可用音色列表
voices = tts.get_voices()
print(voices)
