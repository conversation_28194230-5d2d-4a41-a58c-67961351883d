from typing import Optional, Dict, Any, Union
from pydantic import BaseModel, Field

class TTSOptions(BaseModel):
    """统一的TTS选项参数"""
    # 基本参数
    style: Optional[str] = Field(None, description="语气风格")
    speed: float = Field(1.0, ge=0.5, le=2.0, description="语速")
    volume: float = Field(1.0, gt=0, le=10.0, description="音量")
    pitch: float = Field(0, ge=-12, le=12, description="语调")
    
    # 音频设置
    audio_format: str = Field("mp3", description="音频格式")
    bitrate: Optional[int] = Field(None, description="比特率")
    channel: Optional[int] = Field(1, description="声道数")
    
    # 特殊功能
    streaming: bool = Field(False, description="是否流式输出")
    subtitle: bool = Field(False, description="是否生成字幕")
    
    # 厂商特定参数
    provider_options: Dict[str, Any] = Field(default_factory=dict, description="厂商特定参数")

class TTSResult:
    """统一的TTS结果"""
    def __init__(
        self, 
        audio_data: bytes, 
        subtitle_url: Optional[str] = None, 
        extra_info: Optional[Dict] = None
    ):
        self.audio_data = audio_data
        self.subtitle_url = subtitle_url
        self.extra_info = extra_info or {}
    
    @property
    def has_subtitle(self) -> bool:
        """是否包含字幕"""
        return self.subtitle_url is not None
