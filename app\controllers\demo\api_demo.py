from flask import request, g
from flask.typing import ResponseReturnValue
from flask_restful import Resource
from marshmallow import ValidationError
from app.controllers.wraps import validate_json
from app.utils.response import APIResponse
from app.schemas.demo import ProductSchema, ProductQuerySchema, CategorySchema



class HelloAPI(Resource):
    """问候API"""
    
    def get(self) -> ResponseReturnValue:
        """返回问候消息"""
        name = request.args.get('name', 'World')
        return APIResponse.success(
            message=f"Hello, {name}!",
            data={"name": name}
        )

class UserAPI(Resource):
    """用户API"""
    
    @validate_json("username", "email")
    def post(self) -> ResponseReturnValue:
        """创建新用户"""
        data = request.get_json()
        return APIResponse.success(
            message="User created successfully",
            data={
                "username": data["username"],
                "email": data["email"],
                "age": data.get("age")
            }
        )

class RequestInfoAPI(Resource):
    """请求信息API"""
    
    def get(self) -> ResponseReturnValue:
        """获取当前请求的信息，包括请求ID"""
        return APIResponse.success(
            message="Request info retrieved",
            data={
                "request_id": g.request_id,
                "method": request.method,
                "path": request.path,
                "headers": dict(request.headers)
            }
        )

class ProductAPI(Resource):
    """商品API"""
    
    def post(self) -> ResponseReturnValue:
        """创建新商品"""
        schema = ProductSchema()
        data = schema.load(request.get_json())
        result = schema.dump(data)
        
        return APIResponse.success(
            message="商品创建成功",
            data=result
        )
    
    def get(self) -> ResponseReturnValue:
        """获取商品列表"""
        schema = ProductQuerySchema()
        params = schema.load(request.args)
        
        # 示例数据
        products = [
            {
                "id": 1,
                "name": "iPhone 13",
                "price": 5999.00,
                "stock": 100,
                "category": {
                    "name": "手机",
                    "description": "智能手机"
                },
                "tags": ["苹果", "手机", "5G"],
                "status": "active"
            },
            {
                "id": 2,
                "name": "MacBook Pro",
                "price": 12999.00,
                "stock": 50,
                "category": {
                    "name": "笔记本",
                    "description": "苹果笔记本"
                },
                "tags": ["苹果", "笔记本", "电脑"],
                "status": "active"
            }
        ]
        
        # 根据查询参数筛选商品
        if params.get('name'):
            products = [p for p in products if params['name'].lower() in p['name'].lower()]
        if params.get('min_price') is not None:
            products = [p for p in products if p['price'] >= params['min_price']]
        if params.get('max_price') is not None:
            products = [p for p in products if p['price'] <= params['max_price']]
        if params.get('category_name'):
            products = [p for p in products if p['category']['name'] == params['category_name']]
        if params.get('status'):
            products = [p for p in products if p['status'] == params['status']]
        
        # 分页
        page = params.get('page', 1)
        size = params.get('size', 10)
        start = (page - 1) * size
        end = start + size
        paginated_products = products[start:end]
        
        return APIResponse.success(
            message="获取商品列表成功",
            data={
                "items": paginated_products,
                "page": page,
                "size": size,
                "total": len(products)
            }
        )

class ProductBulkAPI(Resource):
    """商品批量操作API"""
    
    def post(self) -> ResponseReturnValue:
        """批量创建商品"""
        data = request.get_json()
        products_data = data.get('products', [])
        
        schema = ProductSchema()
        results = []
        errors = []
        
        for i, product_data in enumerate(products_data):
            try:
                validated_data = schema.load(product_data)
                results.append(schema.dump(validated_data))
            except ValidationError as e:
                errors.append({
                    "index": i,
                    "data": product_data,
                    "errors": e.messages
                })
        
        if errors:
            return APIResponse.error(
                message="部分商品数据验证失败",
                data={
                    "success_count": len(results),
                    "error_count": len(errors),
                    "errors": errors
                }
            )
        
        return APIResponse.success(
            message="商品批量创建成功",
            data={
                "total": len(results),
                "items": results
            }
        )

