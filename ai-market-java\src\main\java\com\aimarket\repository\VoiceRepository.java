package com.aimarket.repository;

import com.aimarket.entity.Voice;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 音色数据访问层
 * 
 * <AUTHOR> Market Team
 */
@Repository
public interface VoiceRepository extends JpaRepository<Voice, Long> {

    /**
     * 根据音色ID查找音色
     */
    Optional<Voice> findByVoiceId(String voiceId);

    /**
     * 根据提供商查找音色
     */
    List<Voice> findByProviderAndIsActiveTrueOrderBySortOrderAsc(String provider);

    /**
     * 根据语言ID查找音色
     */
    List<Voice> findByLanguageIdAndIsActiveTrueOrderBySortOrderAsc(Long languageId);

    /**
     * 根据语言代码查找音色
     */
    @Query("SELECT v FROM Voice v JOIN v.language l WHERE l.code = :languageCode AND v.isActive = true ORDER BY v.sortOrder")
    List<Voice> findByLanguageCodeAndIsActiveTrue(@Param("languageCode") String languageCode);

    /**
     * 根据提供商和语言查找音色
     */
    @Query("SELECT v FROM Voice v JOIN v.language l WHERE v.provider = :provider AND l.code = :languageCode AND v.isActive = true ORDER BY v.sortOrder")
    List<Voice> findByProviderAndLanguageCode(@Param("provider") String provider, @Param("languageCode") String languageCode);

    /**
     * 根据性别查找音色
     */
    List<Voice> findByGenderAndIsActiveTrueOrderBySortOrderAsc(String gender);

    /**
     * 根据风格查找音色
     */
    List<Voice> findByStyleAndIsActiveTrueOrderBySortOrderAsc(String style);

    /**
     * 查找高级音色
     */
    List<Voice> findByIsPremiumTrueAndIsActiveTrueOrderBySortOrderAsc();

    /**
     * 分页查找活跃音色
     */
    Page<Voice> findByIsActiveTrue(Pageable pageable);

    /**
     * 根据名称模糊查询音色
     */
    @Query("SELECT v FROM Voice v WHERE v.name LIKE %:name% AND v.isActive = true ORDER BY v.sortOrder")
    List<Voice> findByNameLikeAndIsActiveTrue(@Param("name") String name);

    /**
     * 根据标签查找音色
     */
    @Query("SELECT v FROM Voice v WHERE v.tags LIKE %:tag% AND v.isActive = true ORDER BY v.sortOrder")
    List<Voice> findByTagsContainingAndIsActiveTrue(@Param("tag") String tag);

    /**
     * 查找所有活跃音色，按语言分组
     */
    @Query("SELECT v FROM Voice v JOIN FETCH v.language l WHERE v.isActive = true AND l.isActive = true ORDER BY l.sortOrder, v.sortOrder")
    List<Voice> findAllActiveVoicesWithLanguage();

    /**
     * 统计指定提供商的音色数量
     */
    @Query("SELECT COUNT(v) FROM Voice v WHERE v.provider = :provider AND v.isActive = true")
    long countByProviderAndIsActiveTrue(@Param("provider") String provider);

    /**
     * 统计指定语言的音色数量
     */
    @Query("SELECT COUNT(v) FROM Voice v JOIN v.language l WHERE l.code = :languageCode AND v.isActive = true")
    long countByLanguageCodeAndIsActiveTrue(@Param("languageCode") String languageCode);

    /**
     * 检查音色ID是否存在
     */
    boolean existsByVoiceId(String voiceId);
}
