package com.aimarket.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * AI Market 自定义配置属性
 * 
 * <AUTHOR> Market Team
 */
@Component
@ConfigurationProperties(prefix = "aimarket")
public class AiMarketProperties {

    private Tts tts = new Tts();
    private Audio audio = new Audio();
    private Image image = new Image();
    private Security security = new Security();

    // Getters and Setters
    public Tts getTts() {
        return tts;
    }

    public void setTts(Tts tts) {
        this.tts = tts;
    }

    public Audio getAudio() {
        return audio;
    }

    public void setAudio(Audio audio) {
        this.audio = audio;
    }

    public Image getImage() {
        return image;
    }

    public void setImage(Image image) {
        this.image = image;
    }

    public Security getSecurity() {
        return security;
    }

    public void setSecurity(Security security) {
        this.security = security;
    }

    /**
     * TTS相关配置
     */
    public static class Tts {
        private String defaultProvider = "minimax";
        private int timeout = 30;
        private int retryCount = 3;
        private Cache cache = new Cache();

        // Getters and Setters
        public String getDefaultProvider() {
            return defaultProvider;
        }

        public void setDefaultProvider(String defaultProvider) {
            this.defaultProvider = defaultProvider;
        }

        public int getTimeout() {
            return timeout;
        }

        public void setTimeout(int timeout) {
            this.timeout = timeout;
        }

        public int getRetryCount() {
            return retryCount;
        }

        public void setRetryCount(int retryCount) {
            this.retryCount = retryCount;
        }

        public Cache getCache() {
            return cache;
        }

        public void setCache(Cache cache) {
            this.cache = cache;
        }

        public static class Cache {
            private long voicesTtl = 3600;
            private long configTtl = 1800;

            public long getVoicesTtl() {
                return voicesTtl;
            }

            public void setVoicesTtl(long voicesTtl) {
                this.voicesTtl = voicesTtl;
            }

            public long getConfigTtl() {
                return configTtl;
            }

            public void setConfigTtl(long configTtl) {
                this.configTtl = configTtl;
            }
        }
    }

    /**
     * 音频处理相关配置
     */
    public static class Audio {
        private List<String> supportedFormats = List.of("mp3", "wav", "pcm");
        private String defaultFormat = "mp3";
        private String storagePath = "/tmp/audio";
        private String urlPrefix = "http://localhost:8080/ai/market/audio/files";

        // Getters and Setters
        public List<String> getSupportedFormats() {
            return supportedFormats;
        }

        public void setSupportedFormats(List<String> supportedFormats) {
            this.supportedFormats = supportedFormats;
        }

        public String getDefaultFormat() {
            return defaultFormat;
        }

        public void setDefaultFormat(String defaultFormat) {
            this.defaultFormat = defaultFormat;
        }

        public String getStoragePath() {
            return storagePath;
        }

        public void setStoragePath(String storagePath) {
            this.storagePath = storagePath;
        }

        public String getUrlPrefix() {
            return urlPrefix;
        }

        public void setUrlPrefix(String urlPrefix) {
            this.urlPrefix = urlPrefix;
        }
    }

    /**
     * 图像处理相关配置
     */
    public static class Image {
        private List<String> supportedFormats = List.of("jpg", "jpeg", "png", "gif");
        private int maxFileSize = 10;
        private String storagePath = "/tmp/images";

        // Getters and Setters
        public List<String> getSupportedFormats() {
            return supportedFormats;
        }

        public void setSupportedFormats(List<String> supportedFormats) {
            this.supportedFormats = supportedFormats;
        }

        public int getMaxFileSize() {
            return maxFileSize;
        }

        public void setMaxFileSize(int maxFileSize) {
            this.maxFileSize = maxFileSize;
        }

        public String getStoragePath() {
            return storagePath;
        }

        public void setStoragePath(String storagePath) {
            this.storagePath = storagePath;
        }
    }

    /**
     * 安全相关配置
     */
    public static class Security {
        private boolean apiKeyEnabled = false;
        private String allowedOrigins = "*";
        private String allowedHeaders = "*";
        private String allowedMethods = "GET,POST,PUT,DELETE,OPTIONS,PATCH";

        // Getters and Setters
        public boolean isApiKeyEnabled() {
            return apiKeyEnabled;
        }

        public void setApiKeyEnabled(boolean apiKeyEnabled) {
            this.apiKeyEnabled = apiKeyEnabled;
        }

        public String getAllowedOrigins() {
            return allowedOrigins;
        }

        public void setAllowedOrigins(String allowedOrigins) {
            this.allowedOrigins = allowedOrigins;
        }

        public String getAllowedHeaders() {
            return allowedHeaders;
        }

        public void setAllowedHeaders(String allowedHeaders) {
            this.allowedHeaders = allowedHeaders;
        }

        public String getAllowedMethods() {
            return allowedMethods;
        }

        public void setAllowedMethods(String allowedMethods) {
            this.allowedMethods = allowedMethods;
        }
    }
}
