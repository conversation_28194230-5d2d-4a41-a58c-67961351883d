package com.aimarket.core.tts.exception;

/**
 * TTS提供商异常
 * 当特定提供商出现问题时抛出
 * 
 * <AUTHOR> Market Team
 */
public class TtsProviderException extends TtsException {

    public TtsProviderException(String message, String providerName) {
        super(message, null, providerName);
    }

    public TtsProviderException(String message, Throwable cause, String providerName) {
        super(message, cause, null, providerName);
    }

    public TtsProviderException(String message, String errorCode, String providerName) {
        super(message, errorCode, providerName);
    }

    public TtsProviderException(String message, Throwable cause, String errorCode, String providerName) {
        super(message, cause, errorCode, providerName);
    }
}
