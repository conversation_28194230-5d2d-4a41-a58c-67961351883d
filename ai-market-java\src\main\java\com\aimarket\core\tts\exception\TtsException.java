package com.aimarket.core.tts.exception;

/**
 * TTS异常基类
 * 
 * <AUTHOR> Market Team
 */
public class TtsException extends RuntimeException {

    private String errorCode;
    private String providerName;

    public TtsException(String message) {
        super(message);
    }

    public TtsException(String message, Throwable cause) {
        super(message, cause);
    }

    public TtsException(String message, String errorCode) {
        super(message);
        this.errorCode = errorCode;
    }

    public TtsException(String message, String errorCode, String providerName) {
        super(message);
        this.errorCode = errorCode;
        this.providerName = providerName;
    }

    public TtsException(String message, Throwable cause, String errorCode, String providerName) {
        super(message, cause);
        this.errorCode = errorCode;
        this.providerName = providerName;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getProviderName() {
        return providerName;
    }

    public void setProviderName(String providerName) {
        this.providerName = providerName;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName()).append(": ").append(getMessage());
        
        if (errorCode != null) {
            sb.append(" [错误码: ").append(errorCode).append("]");
        }
        
        if (providerName != null) {
            sb.append(" [提供商: ").append(providerName).append("]");
        }
        
        return sb.toString();
    }
}
