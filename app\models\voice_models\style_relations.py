"""声音、语气关联表模型"""

from sqlalchemy import String, Integer, ForeignKey
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.models.base import BaseModel


class GkVoiceStyleRelations(BaseModel):
    """声音、语气关联表"""

    __tablename__ = 'gk_voice_style_relations'

    id: Mapped[int] = mapped_column(
        Integer,
        primary_key=True,
        autoincrement=True,  # 自动递增，与SQL定义保持一致
        comment='主键ID'
    )
    voice_id: Mapped[int] = mapped_column(
        Integer,
        nullable=True,
        comment='声音id'
    )
    style: Mapped[str] = mapped_column(
        String(255),
        nullable=True,
        comment='风格语气'
    )
    style_name: Mapped[str] = mapped_column(
        String(255),
        nullable=True,
        comment='语气名称'
    )
    speaker: Mapped[str] = mapped_column(
        String(255),
        nullable=True,
        comment='声音角色'
    )
    audio_url: Mapped[str] = mapped_column(
        String(255),
        nullable=True,
        comment='声音demo地址'
    )

    def __repr__(self) -> str:
        return f"<GkVoiceStyleRelations {self.id}: voice_id={self.voice_id}, style={self.style}, style_name={self.style_name}>"
