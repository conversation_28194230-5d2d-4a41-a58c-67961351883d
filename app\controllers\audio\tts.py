from flask import request
from flask.typing import ResponseReturnValue
from flask_restful import Resource
from app.controllers.wraps import validate_json
from app.utils.response import APIResponse
from app.schemas.tts import TTSRequestSchema, TTSResponseSchema
from app.services.tts import TTSService

class VoiceAPI(Resource):
    """音色 API"""
    
    def __init__(self):
        """初始化"""
        self.tts_service = TTSService()
        
    def get(self) -> ResponseReturnValue:
        """获取所有音色信息
        
        返回:
            按语言分组的声音信息列表，包含语言、供应商、音色、价格等信息
        """
        # 调用服务层获取所有音色信息
        result = self.tts_service.get_all_voices()
        
        return APIResponse.success(
            message="获取音色信息成功",
            data=result
        )


class TTSAPI(Resource):
    """TTS API"""
    
    def __init__(self):
        """初始化"""
        self.tts_service = TTSService()
        self.request_schema = TTSRequestSchema()
        self.response_schema = TTSResponseSchema()
        
    @validate_json("text", "voice_id")
    def post(self) -> ResponseReturnValue:
        """文本转语音
        
        请求参数:
            text: 要转换的文本
            voice_id: 声音ID
            style: 语气
            speed: 语速(可选,默认1.0)
            volume: 音量(可选,默认1.0)
            pitch: 音调(可选,默认0)
            audio_format: 音频格式(可选,默认mp3)
            sample_rate: 采样率范围【8000，16000，22050，24000，32000，44100】可选，默认为32000                暂不开放
            bit_rate:比特率范围【32000，64000，128000，256000】可选，默认值为128000。该参数仅对mp3格式的音频生效。  暂不开放
            channel: 声道数默认1：单声道，可选：1：单声道  2：双声道

            streaming: 是否流式输出(可选,默认False)  暂不开放
            subtitle: 是否生成字幕(可选,默认False)
            output_format: 输出形式(可选，默认hex)  暂不开放
            
        返回:
            audio_url: 音频URL
            subtitle_url: 字幕URL(如果请求生成)
            duration: 音频时长
            format: 音频格式
            size: 文件大小
        """
        # 验证请求参数
        params = self.request_schema.load(request.get_json())
        
        # 调用服务层处理
        result = self.tts_service.text_to_speech(params)
        
        # 序列化响应数据
        response_data = self.response_schema.dump(result)
        
        return APIResponse.success(
            message="文本转语音成功",
            data=response_data
        )
