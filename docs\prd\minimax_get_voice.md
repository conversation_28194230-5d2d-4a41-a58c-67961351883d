查询可用Voice ID

该API支持查询当前账号下可调用的全部音色ID（voice_id）。
包括系统音色、快速克隆音色、文生音色接口生成的音色、音乐生成接口的人声音色以及伴奏音色。

API说明
POST https://api.minimax.chat/v1/get_voice

请求体（Request）参数
Authorizationstring必填
HTTP：Bearer Auth
- Security Scheme Type: http
- HTTP Authorization Scheme: Bearer
API_key，可在账户管理>接口密钥中查看。
voice_typestring必填
欲查询音色类型，支持以下取值：
"system"（系统音色），
"voice_cloning"（快速复刻的音色），
"voice_generation"（文生音色接口生成的音色），
"music_generation"（音乐生成产生的人声或者伴奏音色），
"all"（以上全部）。

返回（Response）参数
system_voicearray
包含系统预定义的音色。每个语音具有唯一的voice_id，voice_name和描述信息（如性别、年龄等）。
隐藏参数
voice_idstring
音色voice_id。
voice_namestring
音色名称（非调用ID）。
descriptionarray
音色描述。
voice_cloningarray
包含音色快速复刻的音色数据，返回复刻的语色ID、描述信息和复刻音色创建时间。
隐藏参数
voice_idstring
快速复刻音色voice_id。
descriptionarray
复刻时填写的音色描述。
created_timestring
创建时间，格式yyyy-mm-dd（音色复刻请求提交的时间，非首次调用生效激活时间）。
voice_generationarray
包含音色生成接口产生的音色数据，包含音色ID、音色描述与生成时间。
隐藏参数
voice_idstring
音色voice_id。
descriptionarray
生成音色时填写的音色描述。
created_timestring
创建时间，格式yyyy-mm-dd（音色生成请求提交的时间，非首次调用时间）。
music_generationarray
包含音乐生成接口产生的音色信息。包含人声音色ID、伴奏ID、音色描述与生成时间。
隐藏参数
voice_idstring
人声音色voice_id。
instrumental_idstring
伴奏voice_id。
created_timestring
音色创建时间，格式yyyy-mm-dd（音色生成请求提交的时间，非首次调用时间）。

~~~
import requests

api_key = "请填写你的api key"

url = f'https://api.minimax.chat/v1/get_voice'
headers = {
    'authority': 'api.minimax.chat',
    'Authorization': f'Bearer {api_key}'
}

data = {
    'voice_type': 'all'
}

response = requests.post(url, headers=headers, data=data)
~~~