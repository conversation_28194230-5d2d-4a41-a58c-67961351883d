from typing import Dict, Type
import random
from .base import BaseTTS
from .exceptions import TTSConfigException

class TTSFactory:
    """TTS工厂类"""
    # 类变量用于存储所有注册的提供商
    _providers: Dict[str, Type[BaseTTS]] = {}
    
    def __init__(self):
        """初始化工厂类"""
        from .config import TTSConfigManager
        self._config_manager = TTSConfigManager()
    
    @classmethod
    def register(cls, name: str) -> callable:
        """
        注册提供商装饰器
        
        Args:
            name: 提供商名称
            
        Returns:
            callable: 装饰器函数
        """
        def decorator(provider_class: Type[BaseTTS]):
            cls._providers[name] = provider_class
            return provider_class
        return decorator
    
    def create(self, provider: str) -> BaseTTS:
        """
        创建TTS实例
        
        Args:
            provider: 提供商名称
            
        Returns:
            BaseTTS: TTS实例
            
        Raises:
            TTSConfigException: 当提供商不存在或配置错误时抛出
        """
        if provider not in self._providers:
            raise TTSConfigException(f"Unknown provider: {provider}", provider)
            
        provider_class = self._providers[provider]
        provider_config = self._config_manager.get_provider(provider)
        
        if not provider_config:
            raise TTSConfigException(f"No configuration found for provider: {provider}", provider)
            
        if not provider_config.keys:
            raise TTSConfigException(f"No API keys configured for provider: {provider}", provider)
            
        key_config = random.choice(provider_config.keys)
        return provider_class(**key_config)
