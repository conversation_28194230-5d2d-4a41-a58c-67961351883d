package com.aimarket;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * AI Market Java版本主启动类
 * 
 * 基于Spring Boot 3的AI服务平台，提供TTS、图像处理等AI能力的API服务
 * 
 * <AUTHOR> Market Team
 * @version 1.0.0
 */
@SpringBootApplication
@EnableCaching
@EnableJpaAuditing
@EnableAsync
public class AiMarketApplication {

    public static void main(String[] args) {
        SpringApplication.run(AiMarketApplication.class, args);
    }
}
