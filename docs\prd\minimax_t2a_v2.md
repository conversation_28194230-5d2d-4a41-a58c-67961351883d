HTTP接口
接口地址：https://api.minimax.chat/v1/t2a_v2
接口参数说明
请求体（Request）参数
Authorizationstring必填
HTTP：Bearer Auth
- Security Scheme Type: http
- HTTP Authorization Scheme: Bearer
API_key，可在账户管理>接口密钥中查看。

Content-Typeapplication/json必填
Content-Type。
Groupid使用发放的值必填
用户所属的组。使用发放的值。该值应拼接在调用API的url末尾。
modelstring必填
请求的模型版本：speech-02-hd、speech-02-turbo、speech-01-hd、speech-01-turbo、speech-01-240228、speech-01-turbo-240228

textstring必填
待合成的文本，长度限制<10000字符，段落切换用换行符替代。（如需要控制语音中间隔时间，在字间增加<#x#>,x单位为秒，支持0.01-99.99，最多两位小数）。支持自定义文本与文本之间的语音时间间隔，以实现自定义文本语音停顿时间的效果。需要注意的是文本间隔时间需设置在两个可以语音发音的文本之间，且不能设置多个连续的时间间隔。

voice_setting
隐藏参数
speed范围[0.5,2]，默认值为1.0
生成声音的语速，可选，取值越大，语速越快。
vol范围（0,10]，默认值为1.0
生成声音的音量，可选，取值越大，音量越高。
pitch范围[-12,12]，默认值为0
生成声音的语调，可选，（0为原音色输出，取值需为整数）。
voice_idstring
请求的音色编号。与timber_weights二选一“必填”。

emotionstring
控制合成语音的情绪；
当前支持7种情绪：高兴，悲伤，愤怒，害怕，厌恶，惊讶，中性；
参数范围["happy", "sad", "angry", "fearful", "disgusted", "surprised", "neutral"]
该参数仅对
speech-02-hd，speech-02-turbo，speech-01-turbo，speech-01-hd生效
latex_readbool
控制是否支持朗读latex公式，默认为false。
需注意：
1. 请求中的公式需要在公式的首尾加上$$；
2. 请求中公式若有"\"，需转义成"\\"。
示例：导数的基本公式是$$\\frac{d}{dx}(x^n) = nx^{n-1}$$
english_normalizationbool
该参数支持英语文本规范化，可提升数字阅读场景的性能，但会略微增加延迟。如果未提供，则默认值为 false。


audio_setting
隐藏参数
sample_rate范围【8000，16000，22050，24000，32000，44100】
生成声音的采样率。可选，默认为32000。
bitrate范围【32000，64000，128000，256000】
生成声音的比特率。可选，默认值为128000。该参数仅对mp3格式的音频生效。
formatstring
生成的音频格式。默认mp3，范围[mp3,pcm,flac,wav]。wav仅在非流式输出下支持。
channelint
生成音频的声道数.默认1：单声道，可选：
1：单声道
2：双声道


pronunciation_dict
隐藏参数
tonelist
替换需要特殊标注的文字、符号及对应的注音。
替换发音（调整声调/替换其他字符发音），格式如下：
["燕少飞/(yan4)(shao3)(fei1)","达菲/(da2)(fei1)"，"omg/oh my god"]
声调用数字代替，一声（阴平）为1，二声（阳平）为2，三声（上声）为3，四声（去声）为4），轻声为5。
timber_weights与voice_id二选一必填
隐藏参数
voice_idstring
请求的音色id。须和weight参数同步填写。
weight范围[1,100]
权重，须与voice_id同步填写。最多支持4种音色混合，取值为整数，单一音色取值占比越高，合成音色越像。
streamboolean
是否流式。默认false，即不开启流式。
language_booststring,默认为null
增强对指定的小语种和方言的识别能力，设置后可以提升在指定小语种/方言场景下的语音表现。如果不明确小语种类型，则可以选择"auto"，模型将自主判断小语种类型。支持以下取值：
'Chinese', 'Chinese,Yue', 'English', 'Arabic', 'Russian', 'Spanish', 'French', 'Portuguese', 'German', 'Turkish', 'Dutch', 'Ukrainian', 'Vietnamese', 'Indonesian', 'Japanese', 'Italian', 'Korean', 'Thai', 'Polish', 'Romanian', 'Greek', 'Czech', 'Finnish', 'Hindi', 'auto'
subtitle_enablebool
控制是否开启字幕服务的开关。默认为false。此参数仅对speech-01-turbospeech-01-hd有效。
output_formatstring
控制输出结果形式的参数。可选值为urlhex。默认值为hex。该参数仅在非流式场景生效，流式场景仅支持返回hex形式。


返回(Response)参数
dataobject
data可能返回为null，参考示例代码时，注意进行非空判断。
展开参数
trace_idstring
本次会话的id。用于在咨询/反馈时帮助定位问题。
extra_infoobject
相关额外信息。
隐藏参数
audio_lengthint64
音频时长，精确到毫秒。
audio_sample_rateint64
采样率。
audio_sizeint64
音频大小。单位为字节。
bitrateint64
比特率。
audio_formatstring
生成音频文件的格式。取值范围mp3/pcm/flac。
audio_channelint64
生成音频声道数。1：单声道，2：双声道。
invisible_character_ratiofloat64
非法字符占比。非法字符不超过10%（包含10%），音频会正常生成并返回非法字符占比；最大不超过0.1（10%），超过进行报错。
usage_charactersint64
计费字符数。本次语音生成的计费字符数。
base_respobject
如果请求出错，对应的错误状态码和详情。
隐藏参数
status_codeint64
状态码。1000，未知错误；1001，超时；1002，触发限流；1004，鉴权失败；1039，触发TPM限流；1042，非法字符超过10%；2013，输入格式信息不正常。
status_msgstring
状态详情。


### 接口调用示例(非流式)
~~~
curl --location 'https://api.minimax.chat/v1/t2a_v2?GroupId=${group_id}' \
--header 'Authorization: Bearer $MiniMax_API_KEY' \
--header 'Content-Type: application/json' \
--data '{
    "model":"speech-02-hd",
    "text":"真正的危险不是计算机开始像人一样思考，而是人开始像计算机一样思考。计算机只是可以帮我们处理一些简单事务。",
    "stream":false,
    "voice_setting":{
        "voice_id":"male-qn-qingse",
        "speed":1,
        "vol":1,
        "pitch":0,
        "emotion":"happy"
    },
    "pronunciation_dict":{
        "tone":["处理/(chu3)(li3)", "危险/dangerous"]
    },
    "audio_setting":{
        "sample_rate":32000,
        "bitrate":128000,
        "format":"mp3",
        "channel":1
    }
  }'
~~~

