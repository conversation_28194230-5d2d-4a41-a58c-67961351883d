"""声音、模型关联表模型"""

from decimal import Decimal
from sqlalchemy import String, Integer, Numeric, ForeignKey
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.models.base import BaseModel


class GkVoiceModel(BaseModel):
    """声音、模型关联表"""

    __tablename__ = 'gk_voice_model'

    id: Mapped[int] = mapped_column(
        Integer,
        primary_key=True,
        autoincrement=False,  # 不自动递增，与SQL定义保持一致
        comment='主键ID'
    )
    voice_id: Mapped[int] = mapped_column(
        Integer,
        nullable=True,
        comment='声音id'
    )
    model: Mapped[str] = mapped_column(
        String(255),
        nullable=True,
        comment='模型名称'
    )
    price: Mapped[Decimal] = mapped_column(
        Numeric(10, 2),
        nullable=True,
        comment='价格'
    )

    def __repr__(self) -> str:
        return f"<GkVoiceModel {self.id}: voice_id={self.voice_id}, model={self.model}, price={self.price}>"
