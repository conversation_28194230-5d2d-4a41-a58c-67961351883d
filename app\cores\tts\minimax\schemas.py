import os
from typing import Optional, List, Union
from pydantic import BaseModel, Field
from datetime import datetime

class VoiceSetting(BaseModel):
    """语音设置参数"""
    voice_id: str = Field(..., description="音色编号")
    speed: Optional[float] = Field(1.0, ge=0.5, le=2.0, description="语速")
    vol: Optional[float] = Field(1.0, gt=0, le=10.0, description="音量")
    pitch: Optional[int] = Field(0, ge=-12, le=12, description="语调")
    emotion: Optional[str] = Field("neutral", description="情绪")

class PronunciationDict(BaseModel):
    """发音字典设置"""
    tone: Optional[List[str]] = Field(None, description="替换需要特殊标注的文字及对应注音")

class AudioSetting(BaseModel):
    """音频设置参数"""
    sample_rate: Optional[int] = Field(32000, description="采样率")
    bitrate: Optional[int] = Field(128000, description="比特率")
    format: Optional[str] = Field("mp3", description="音频格式")
    channel: Optional[int] = Field(1, description="声道数")

class TTSRequest(BaseModel):
    """TTS请求参数"""
    model: Optional[str] = Field(os.getenv('MINIMAX_DEFAULT_MODEL'), description="模型版本")
    text: str = Field(..., max_length=10000, description="待合成文本")
    stream: Optional[bool] = Field(False, description="是否流式输出")
    voice_setting: VoiceSetting
    pronunciation_dict: Optional[PronunciationDict] = None
    audio_setting: Optional[AudioSetting] = None
    latex_read: Optional[bool] = Field(False, description="是否支持朗读latex公式")
    english_normalization: Optional[bool] = Field(False, description="是否支持英语文本规范化")
    language_boost: Optional[str] = Field(None, description="增强指定的小语种和方言识别能力")
    subtitle_enable: Optional[bool] = Field(False, description="是否开启字幕服务")
    output_format: Optional[str] = Field("hex", description="输出结果形式")

class ExtraInfo(BaseModel):
    """额外信息"""
    audio_length: Optional[int] = None
    audio_sample_rate: Optional[int] = None
    audio_size: Optional[int] = None
    bitrate: Optional[int] = None
    audio_format: Optional[str] = None
    audio_channel: Optional[int] = None
    invisible_character_ratio: Optional[float] = None
    usage_characters: Optional[int] = None

class BaseResp(BaseModel):
    """基础响应"""
    status_code: Optional[int] = None
    status_msg: Optional[str] = None

class TTSResponse(BaseModel):
    """TTS响应参数"""
    data: Optional[dict] = None
    trace_id: Optional[str] = None
    extra_info: Optional[ExtraInfo] = None
    base_resp: Optional[BaseResp] = None

class SystemVoice(BaseModel):
    """系统预定义音色"""
    voice_id: str = Field(..., description="音色voice_id")
    voice_name: str = Field(..., description="音色名称")
    description: List[str] = Field(..., description="音色描述")

class VoiceCloning(BaseModel):
    """快速复刻音色数据"""
    voice_id: str = Field(..., description="快速复刻音色voice_id")
    description: List[str] = Field(..., description="复刻时填写的音色描述")
    created_time: str = Field(..., description="创建时间，格式yyyy-mm-dd")

class VoiceGeneration(BaseModel):
    """音色生成接口产生的音色数据"""
    voice_id: str = Field(..., description="音色voice_id")
    description: List[str] = Field(..., description="生成音色时填写的音色描述")
    created_time: str = Field(..., description="创建时间，格式yyyy-mm-dd")

class MusicGeneration(BaseModel):
    """音乐生成接口产生的音色信息"""
    voice_id: str = Field(..., description="人声音色voice_id")
    instrumental_id: str = Field(..., description="伴奏voice_id")
    created_time: str = Field(..., description="音色创建时间，格式yyyy-mm-dd")

class GetVoiceResponse(BaseModel):
    """获取音色列表响应"""
    system_voice: Optional[List[SystemVoice]] = Field(None, description="系统预定义音色列表")
    voice_cloning: Optional[List[VoiceCloning]] = Field(None, description="快速复刻音色列表")
    voice_generation: Optional[List[VoiceGeneration]] = Field(None, description="音色生成接口产生的音色列表")
    music_generation: Optional[List[MusicGeneration]] = Field(None, description="音乐生成接口产生的音色列表")
    base_resp: Optional[BaseResp] = None
