from typing import Optional, List
from pydantic import BaseModel, Field

class AudioSetting(BaseModel):
    """音频设置参数"""
    audio_type: str = Field("mp3", description="音频格式")
    rate: Optional[int] = Field(None, description="采样率")
    volume: Optional[float] = Field(1.0, gt=0.1, le=1.0, description="音量")
    bitrate: Optional[int] = Field(128000, description="比特率")
    channel: Optional[int] = Field(1, description="声道数")

class SymbolSilSetting(BaseModel):
    """停顿符号设置"""
    semi: Optional[int] = Field(None, description="分号停顿时长(ms)")
    exclamation: Optional[int] = Field(None, description="感叹号停顿时长(ms)")
    question: Optional[int] = Field(None, description="问号停顿时长(ms)")
    comma: Optional[int] = Field(None, description="逗号停顿时长(ms)")
    stop: Optional[int] = Field(None, description="句号停顿时长(ms)")
    pause: Optional[int] = Field(None, description="顿号停顿时长(ms)")

    def to_string(self) -> Optional[str]:
        """转换为API需要的字符串格式"""
        if not any([self.semi, self.exclamation, self.question, 
                   self.comma, self.stop, self.pause]):
            return None
            
        parts = []
        if self.semi:
            parts.append(f"semi_{self.semi}")
        if self.exclamation:
            parts.append(f"exclamation_{self.exclamation}")
        if self.question:
            parts.append(f"question_{self.question}")
        if self.comma:
            parts.append(f"comma_{self.comma}")
        if self.stop:
            parts.append(f"stop_{self.stop}")
        if self.pause:
            parts.append(f"pause_{self.pause}")
            
        return ",".join(parts)

class TTSRequest(BaseModel):
    """TTS请求参数"""
    text: str = Field(..., description="要合成的文本内容")
    speaker: str = Field("galaxy_fastv2_moqingyan", description="发音人")
    audio_type: str = Field("mp3", description="音频格式")
    speed: float = Field(1.0, ge=0.5, le=2.0, description="语速")
    volume: Optional[float] = Field(1.0, gt=0.1, le=1.0, description="音量")
    pitch: Optional[float] = Field(0, ge=-10, le=10, description="语调")
    rate: Optional[int] = Field(None, description="采样率")
    convert: Optional[str] = Field(None, description="是否转化为机器声")
    symbol_sil: Optional[str] = Field(None, description="符号停顿时长映射")
    ignore_limit: Optional[bool] = Field(True, description="是否限制字符数")
    gen_srt: Optional[bool] = Field(False, description="是否生成字幕文件")
    merge_symbol: Optional[bool] = Field(True, description="是否开启粗粒度合成")
    streaming: Optional[bool] = Field(False, description="是否流式输出")

    def model_dump(self, *args, **kwargs) -> dict:
        """重写model_dump方法，移除None值"""
        data = super().model_dump(*args, **kwargs)
        return {k: v for k, v in data.items() if v is not None}

class ErrorMessage(BaseModel):
    """错误信息"""
    code: int = Field(..., description="错误码")
    desc: str = Field(..., description="错误描述")

class ErrorResponse(BaseModel):
    """错误响应"""
    status: str = Field("error", description="状态")
    errorMessage: ErrorMessage = Field(..., description="错误信息")

class ExtraInfo(BaseModel):
    """额外信息"""
    audio_length: Optional[int] = Field(None, description="音频长度")
    audio_sample_rate: Optional[int] = Field(None, description="音频采样率")
    audio_size: Optional[int] = Field(None, description="音频大小")
    bitrate: Optional[int] = Field(None, description="比特率")
    audio_format: Optional[str] = Field(None, description="音频格式")
    audio_channel: Optional[int] = Field(None, description="声道数")
    usage_characters: Optional[int] = Field(None, description="使用的字符数")
