from app.cores.tts.moyin import MoyinTTS, TTSRequest, SymbolSilSetting

appkey = '24FB1B9C016369E0846BBC4B80CF24E8'

# 创建TTS客户端
tts = MoyinTTS(appkey="your_appkey", secret="your_secret")

# 创建请求
request = TTSRequest(
    text="要转换的文本",
    speaker="galaxy_fastv2_moqingyan",
    audio_type="mp3",
    speed=1.0
)

# 设置停顿符号（可选）
symbol_sil = SymbolSilSetting(
    semi=200,
    comma=200,
    stop=300
)

# 转换文本为语音
audio_data = tts.text_to_speech(request, symbol_sil=symbol_sil)

# 如果需要生成字幕
request.gen_srt = True
audio_data, srt_url = tts.text_to_speech(request)
