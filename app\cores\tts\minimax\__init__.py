"""
Minimax TTS API 模块

提供与 Minimax TTS API 交互的功能，包括：
- TTS（文本转语音）
- 流式TTS
- 音色查询
"""

from .tts import MinimaxTTS
from .schemas import (
    TTSRequest,
    TTSResponse,
    VoiceSetting,
    AudioSetting,
    PronunciationDict,
    GetVoiceResponse,
    SystemVoice,
    VoiceCloning,
    VoiceGeneration,
    MusicGeneration
)
from .exceptions import MinimaxTTSException

__all__ = [
    'MinimaxTTS',
    'TTSRequest',
    'TTSResponse',
    'VoiceSetting',
    'AudioSetting',
    'PronunciationDict',
    'MinimaxTTSException',
    'GetVoiceResponse',
    'SystemVoice',
    'VoiceCloning',
    'VoiceGeneration',
    'MusicGeneration'
]
