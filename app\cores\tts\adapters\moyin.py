import traceback
from typing import Dict, Optional, List
from ..base import BaseTTS
from ..factory import TTSFactory
from ..exceptions import TTSProviderException
from ..schemas import TTSOptions, TTSResult
from ...tts.moyin.exceptions import MoyinTTSException

@TTSFactory.register("moyin")
class MoyinTTSAdapter(BaseTTS):
    """Moyin TTS适配器"""
    
    def __init__(self, appkey: str, secret: str):
        """
        初始化Moyin TTS适配器
        
        Args:
            appkey: 应用密钥
            secret: 密钥对应的secret
        """
        try:
            from ...tts.moyin.tts import MoyinTTS
            self.tts = MoyinTTS(appkey, secret)
        except Exception as e:
            raise TTSProviderException(str(e), "moyin")
    
    def text_to_speech(self, text: str, voice_id: str, options: Optional[TTSOptions] = None) -> TTSResult:
        """
        将文本转换为语音
        
        Args:
            text: 要转换的文本
            voice_id: 音色ID
            options: 统一的TTS选项参数
            
        Returns:
            TTSResult: 转换结果，包含音频数据和可选的字幕URL
            
        Raises:
            TTSProviderException: 当调用出错时抛出
        """
        try:
            from ...tts.moyin.schemas import TTSRequest, SymbolSilSetting
            
            options = options or TTSOptions()
            
            # 处理特殊的停顿设置
            symbol_sil = None
            if 'symbol_sil' in options.provider_options:
                symbol_sil = SymbolSilSetting(**options.provider_options['symbol_sil'])

            # 构建Moyin请求
            request = TTSRequest(
                text=text,
                speaker=voice_id,  # 使用统一的voice_id参数
                speed=options.speed,
                volume=options.volume,
                pitch=options.pitch,
                audio_type=options.audio_format,
                streaming=options.streaming,
                gen_srt=options.subtitle,
            )
            
            # 添加其他厂商特定参数
            for key, value in options.provider_options.items():
                if key != 'symbol_sil':  # 跳过已处理的symbol_sil
                    setattr(request, key, value)

            print('moyin_request: {}'.format(request))

            result = self.tts.text_to_speech(request, symbol_sil)
            
            # 处理返回结果
            if isinstance(result, tuple):
                audio_data, subtitle_url = result
                return TTSResult(audio_data=audio_data, subtitle_url=subtitle_url)
            else:
                return TTSResult(audio_data=result)
        except MoyinTTSException as e:
            raise TTSProviderException(str(e), "moyin", e.status_code)
        except Exception as e:
            raise TTSProviderException(str(e), "moyin")
    
    def get_voices(self) -> List[Dict]:
        """
        获取可用音色列表
        
        Returns:
            List[Dict]: 音色列表
            
        Raises:
            TTSProviderException: 当调用出错时抛出
        """
        # 注意：目前Moyin API不支持获取音色列表
        # 返回空列表或者可以在这里硬编码支持的音色
        return []
