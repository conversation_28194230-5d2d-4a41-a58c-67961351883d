package com.aimarket.core.tts;

import com.aimarket.core.tts.exception.TtsException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * TTS工厂类
 * 管理所有TTS提供商的创建和获取
 * 
 * <AUTHOR> Market Team
 */
@Component
public class TtsFactory {

    private static final Logger logger = LoggerFactory.getLogger(TtsFactory.class);

    // 存储所有注册的TTS提供商
    private final Map<String, TtsProvider> providers = new ConcurrentHashMap<>();

    /**
     * 注册TTS提供商
     * 
     * @param provider TTS提供商实例
     */
    public void registerProvider(TtsProvider provider) {
        if (provider == null) {
            throw new IllegalArgumentException("TTS提供商不能为空");
        }
        
        String providerName = provider.getProviderName();
        if (providerName == null || providerName.trim().isEmpty()) {
            throw new IllegalArgumentException("TTS提供商名称不能为空");
        }
        
        providers.put(providerName.toLowerCase(), provider);
        logger.info("注册TTS提供商: {}", providerName);
    }

    /**
     * 获取TTS提供商
     * 
     * @param providerName 提供商名称
     * @return TTS提供商实例
     * @throws TtsException 如果提供商不存在或不可用
     */
    public TtsProvider getProvider(String providerName) {
        if (providerName == null || providerName.trim().isEmpty()) {
            throw new TtsException("提供商名称不能为空");
        }
        
        TtsProvider provider = providers.get(providerName.toLowerCase());
        if (provider == null) {
            throw new TtsException("未找到TTS提供商: " + providerName);
        }
        
        if (!provider.isAvailable()) {
            throw new TtsException("TTS提供商不可用: " + providerName);
        }
        
        return provider;
    }

    /**
     * 创建TTS提供商实例
     * 这是一个便利方法，等同于getProvider
     * 
     * @param providerName 提供商名称
     * @return TTS提供商实例
     */
    public TtsProvider create(String providerName) {
        return getProvider(providerName);
    }

    /**
     * 检查提供商是否存在
     * 
     * @param providerName 提供商名称
     * @return 是否存在
     */
    public boolean hasProvider(String providerName) {
        if (providerName == null || providerName.trim().isEmpty()) {
            return false;
        }
        return providers.containsKey(providerName.toLowerCase());
    }

    /**
     * 获取所有已注册的提供商名称
     * 
     * @return 提供商名称集合
     */
    public Set<String> getRegisteredProviders() {
        return providers.keySet();
    }

    /**
     * 获取可用的提供商数量
     * 
     * @return 可用提供商数量
     */
    public int getAvailableProviderCount() {
        return (int) providers.values().stream()
                .filter(TtsProvider::isAvailable)
                .count();
    }

    /**
     * 获取默认提供商
     * 如果没有指定提供商，返回第一个可用的提供商
     * 
     * @return 默认TTS提供商
     * @throws TtsException 如果没有可用的提供商
     */
    public TtsProvider getDefaultProvider() {
        return providers.values().stream()
                .filter(TtsProvider::isAvailable)
                .findFirst()
                .orElseThrow(() -> new TtsException("没有可用的TTS提供商"));
    }

    /**
     * 移除TTS提供商
     * 
     * @param providerName 提供商名称
     * @return 是否成功移除
     */
    public boolean removeProvider(String providerName) {
        if (providerName == null || providerName.trim().isEmpty()) {
            return false;
        }
        
        TtsProvider removed = providers.remove(providerName.toLowerCase());
        if (removed != null) {
            logger.info("移除TTS提供商: {}", providerName);
            return true;
        }
        return false;
    }

    /**
     * 清空所有提供商
     */
    public void clear() {
        providers.clear();
        logger.info("清空所有TTS提供商");
    }

    /**
     * 获取工厂状态信息
     * 
     * @return 状态信息字符串
     */
    public String getStatus() {
        int totalProviders = providers.size();
        int availableProviders = getAvailableProviderCount();
        
        return String.format("TTS工厂状态 - 总提供商: %d, 可用提供商: %d, 已注册: %s", 
                totalProviders, availableProviders, providers.keySet());
    }
}
