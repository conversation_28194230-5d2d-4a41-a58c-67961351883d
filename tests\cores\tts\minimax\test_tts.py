import pytest
import httpx
from unittest.mock import Mock
from app.cores.tts.minimax import (
    MinimaxTTS,
    TTSRequest,
    TTSResponse,
    VoiceSetting,
    MinimaxTTSException
)

@pytest.fixture
def tts_client():
    """创建 TTS 客户端实例"""
    return MinimaxTTS(
        api_key="test_api_key",
        group_id="test_group_id"
    )

@pytest.fixture
def mock_response():
    """模拟成功的 API 响应"""
    return {
        "data": "base64_encoded_audio_data",
        "trace_id": "test_trace_id",
        "extra_info": {
            "audio_length": 1000,
            "audio_sample_rate": 32000,
            "audio_size": 16000,
            "bitrate": 128000,
            "audio_format": "mp3",
            "audio_channel": 1,
            "invisible_character_ratio": 0.0,
            "usage_characters": 10
        },
        "base_resp": {
            "status_code": None,
            "status_msg": None
        }
    }

@pytest.fixture
def tts_request():
    """创建测试请求数据"""
    return TTSRequest(
        model="speech-02-hd",
        text="你好，世界！",
        voice_setting=VoiceSetting(
            voice_id="male-qn-qingse",
            speed=1.0,
            vol=1.0,
            pitch=0
        )
    )

@pytest.mark.asyncio
async def test_text_to_speech_success(tts_client, tts_request, mock_response, mocker):
    """测试成功调用 text_to_speech"""
    # 模拟 httpx 客户端
    mock_client = Mock()
    mock_response_obj = Mock()
    mock_response_obj.json.return_value = mock_response
    mock_client.post.return_value = mock_response_obj
    
    # 替换 httpx.AsyncClient
    mocker.patch('httpx.AsyncClient', return_value=mock_client)
    
    # 执行测试
    response = await tts_client.text_to_speech(tts_request)
    
    # 验证结果
    assert isinstance(response, TTSResponse)
    assert response.data == "base64_encoded_audio_data"
    assert response.trace_id == "test_trace_id"
    assert response.extra_info.audio_length == 1000
    assert response.extra_info.audio_format == "mp3"

@pytest.mark.asyncio
async def test_text_to_speech_api_error(tts_client, tts_request, mocker):
    """测试 API 错误处理"""
    # 模拟错误响应
    error_response = {
        "base_resp": {
            "status_code": 1004,
            "status_msg": "Authentication failed"
        }
    }
    
    # 模拟 httpx 客户端
    mock_client = Mock()
    mock_response = Mock()
    mock_response.json.return_value = error_response
    mock_client.post.return_value = mock_response
    
    # 替换 httpx.AsyncClient
    mocker.patch('httpx.AsyncClient', return_value=mock_client)
    
    # 验证异常抛出
    with pytest.raises(MinimaxTTSException) as exc_info:
        await tts_client.text_to_speech(tts_request)
    
    assert "AuthenticationError" in str(exc_info.value)
    assert exc_info.value.status_code == 1004

@pytest.mark.asyncio
async def test_stream_text_to_speech(tts_client, tts_request, mocker):
    """测试流式调用"""
    # 模拟音频数据块
    audio_chunks = [b"chunk1", b"chunk2", b"chunk3"]
    
    # 模拟流式响应
    mock_response = Mock()
    mock_response.aiter_bytes.return_value = audio_chunks
    
    # 模拟 httpx 客户端
    mock_client = Mock()
    mock_client.stream.return_value.__aenter__.return_value = mock_response
    
    # 替换 httpx.AsyncClient
    mocker.patch('httpx.AsyncClient', return_value=mock_client)
    
    # 收集流式输出
    chunks = []
    async for chunk in tts_client.stream_text_to_speech(tts_request):
        chunks.append(chunk)
    
    # 验证结果
    assert chunks == audio_chunks
    assert tts_request.stream is True  # 验证流式标志被设置

@pytest.mark.asyncio
async def test_http_error_handling(tts_client, tts_request, mocker):
    """测试 HTTP 错误处理"""
    # 模拟 HTTP 错误
    mock_client = Mock()
    mock_client.post.side_effect = httpx.HTTPStatusError(
        "404 Not Found",
        request=Mock(),
        response=Mock(status_code=404)
    )
    
    # 替换 httpx.AsyncClient
    mocker.patch('httpx.AsyncClient', return_value=mock_client)
    
    # 验证异常抛出
    with pytest.raises(MinimaxTTSException) as exc_info:
        await tts_client.text_to_speech(tts_request)
    
    assert "HTTP错误" in str(exc_info.value)

def test_invalid_request_validation():
    """测试请求参数验证"""
    # 测试缺少必填参数
    with pytest.raises(ValueError):
        TTSRequest(
            model="speech-02-hd",
            text="测试文本"
            # 缺少 voice_setting
        )
    
    # 测试参数范围验证
    with pytest.raises(ValueError):
        TTSRequest(
            model="speech-02-hd",
            text="测试文本",
            voice_setting=VoiceSetting(
                voice_id="test-voice_models",
                speed=3.0,  # 超出范围 [0.5, 2.0]
                vol=1.0,
                pitch=0
            )
        )
