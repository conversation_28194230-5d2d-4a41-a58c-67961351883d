CREATE TABLE `gk_voice` (
  `id` int(11) NOT NULL,
  `voice_name` varchar(255) DEFAULT NULL COMMENT '声音名称',
  `display_name` varchar(255) DEFAULT NULL COMMENT '展示名称',
  `gender` varchar(255) DEFAULT NULL COMMENT '性别',
  `chinese_gender` varchar(255) DEFAULT NULL COMMENT '中文性别',
  `provider` varchar(255) DEFAULT NULL COMMENT '提供商',
  `sample_rate_default` int(10) DEFAULT NULL COMMENT '默认采样率',
  `speaker` varchar(255) DEFAULT NULL COMMENT '音色角色',
  `status` int(10) DEFAULT NULL COMMENT '状态',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='声音音色表';



CREATE TABLE `gk_voice_language` (
  `id` int(11) NOT NULL,
  `language` varchar(255) DEFAULT NULL COMMENT '语言code',
  `language_name` varchar(255) DEFAULT NULL COMMENT '语言名称',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `language` (`language`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='声音提供语言表';



CREATE TABLE `gk_voice_language_relations` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `voice_id` int(10) DEFAULT NULL COMMENT '声音id',
  `language` varchar(255) DEFAULT NULL COMMENT '语言',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=22 DEFAULT CHARSET=utf8 COMMENT='声音音色、语言关联表';


CREATE TABLE `gk_voice_model` (
  `id` int(11) NOT NULL,
  `voice_id` int(10) DEFAULT NULL,
  `model` varchar(255) DEFAULT NULL COMMENT '模型名称',
  `price` decimal(10,2) DEFAULT NULL COMMENT '价格',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='声音、模型关联表';


CREATE TABLE `gk_voice_style_relations` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `voice_id` int(11) DEFAULT NULL COMMENT '声音id',
  `style` varchar(255) DEFAULT NULL COMMENT '风格语气',
  `style_name` varchar(255) DEFAULT NULL COMMENT '语气名称',
  `speaker` varchar(255) DEFAULT NULL COMMENT '声音角色',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8 COMMENT='声音、语气关联表';



基于以上这些表，在/app/models/voice下面建立对应的model