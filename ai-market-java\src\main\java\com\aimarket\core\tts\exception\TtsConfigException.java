package com.aimarket.core.tts.exception;

/**
 * TTS配置异常
 * 当配置信息有误时抛出
 * 
 * <AUTHOR> Market Team
 */
public class TtsConfigException extends TtsException {

    public TtsConfigException(String message) {
        super(message, "CONFIG_ERROR");
    }

    public TtsConfigException(String message, Throwable cause) {
        super(message, cause, "CONFIG_ERROR", null);
    }

    public TtsConfigException(String message, String providerName) {
        super(message, "CONFIG_ERROR", providerName);
    }

    public TtsConfigException(String message, Throwable cause, String providerName) {
        super(message, cause, "CONFIG_ERROR", providerName);
    }
}
